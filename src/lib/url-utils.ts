/**
 * URL 工具函数
 */

/**
 * 验证是否为有效的 R2 图片 URL
 */
export function isValidR2ImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    console.warn('URL validation failed: URL is empty or not a string');
    return false;
  }

  try {
    const urlObj = new URL(url);

    // 检查协议
    if (urlObj.protocol !== 'https:') {
      console.warn('URL validation failed: Protocol is not HTTPS');
      return false;
    }

    // 检查域名是否包含 r2.dev
    if (!urlObj.hostname.includes('r2.dev')) {
      console.warn('URL validation failed: Hostname does not contain r2.dev');
      return false;
    }

    // 检查路径是否包含图片文件扩展名
    const pathname = urlObj.pathname.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));

    if (!hasImageExtension) {
      console.warn('URL validation failed: No valid image extension found');
      return false;
    }

    // 检查路径是否包含 uploads 或 generated 文件夹（表示是我们的文件）
    const hasValidPath = pathname.includes('/uploads/') || pathname.includes('/generated/');

    if (!hasValidPath) {
      console.warn('URL validation failed: Path does not contain /uploads/ or /generated/');
      return false;
    }

    console.log('✅ URL validation passed:', url);
    return true;
  } catch (error) {
    console.error('URL validation error:', error);
    return false;
  }
}

/**
 * 从上传响应中提取图片 URL（不进行验证，只提取）
 */
export function extractImageUrlFromUploadResponse(response: any): string | null {
  try {
    if (response?.data?.uploadResult?.url) {
      return response.data.uploadResult.url;
    }
    return null;
  } catch (error) {
    console.error('Failed to extract image URL:', error);
    return null;
  }
}

/**
 * 验证图片 URL 是否可访问
 */
export async function validateImageUrlAccessibility(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok && response.headers.get('content-type')?.startsWith('image/');
  } catch (error) {
    console.error('Failed to validate image URL accessibility:', error);
    return false;
  }
}

/**
 * 格式化 URL 用于显示
 */
export function formatUrlForDisplay(url: string, maxLength: number = 50): string {
  if (!url) return '';
  
  if (url.length <= maxLength) {
    return url;
  }

  const start = url.substring(0, maxLength / 2);
  const end = url.substring(url.length - maxLength / 2);
  return `${start}...${end}`;
}

/**
 * 从 URL 中提取文件名
 */
export function getFilenameFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.substring(pathname.lastIndexOf('/') + 1);
  } catch (error) {
    return 'unknown';
  }
}

/**
 * 测试 URL 提取功能
 */
export function testUrlExtraction() {
  const mockResponse = {
    "code": 0,
    "message": "ok",
    "data": {
      "uploadResult": {
        "key": "uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-father.png",
        "url": "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-father.png",
        "filename": "v1-father.png"
      },
      "sessionId": "session_1754368436436_lahiz1xcs",
      "userUuid": "0eb3db92-83d7-4725-b084-70570b7373d7",
      "imageType": "father"
    }
  };

  const extractedUrl = extractImageUrlFromUploadResponse(mockResponse);
  const isValid = extractedUrl ? isValidR2ImageUrl(extractedUrl) : false;
  const filename = extractedUrl ? getFilenameFromUrl(extractedUrl) : '';

  console.log('🧪 URL Extraction Test Results:');
  console.log('📥 Mock Response:', mockResponse);
  console.log('🔗 Extracted URL:', extractedUrl);
  console.log('✅ Is Valid:', isValid);
  console.log('📄 Filename:', filename);

  return {
    mockResponse,
    extractedUrl,
    isValid,
    filename,
  };
}
