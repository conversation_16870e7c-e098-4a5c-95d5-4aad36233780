"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface GenderSelectorProps {
  selectedGender: "babyBoy" | "babyGirl" | null;
  onGenderSelect: (gender: "babyBoy" | "babyGirl") => void;
}

export default function GenderSelector({ selectedGender, onGenderSelect }: GenderSelectorProps) {
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-lg">Choose Baby Gender</CardTitle>
        <p className="text-sm text-gray-600">
          Select the preferred gender for your future baby
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <Button
            variant={selectedGender === "babyBoy" ? "default" : "outline"}
            className="h-20 flex flex-col gap-2"
            onClick={() => onGenderSelect("baby<PERSON><PERSON>")}
          >
            <span className="text-2xl">👶🏻</span>
            <span className="font-semibold">Baby <PERSON></span>
            {selectedGender === "babyBoy" && (
              <Badge variant="secondary" className="text-xs">
                Selected
              </Badge>
            )}
          </Button>
          
          <Button
            variant={selectedGender === "babyGirl" ? "default" : "outline"}
            className="h-20 flex flex-col gap-2"
            onClick={() => onGenderSelect("babyGirl")}
          >
            <span className="text-2xl">👶🏻</span>
            <span className="font-semibold">Baby Girl</span>
            {selectedGender === "babyGirl" && (
              <Badge variant="secondary" className="text-xs">
                Selected
              </Badge>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
