"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { ScrollArea } from "@/components/ui/scroll-area";
import { FileText, X, Trash2 } from "lucide-react";

interface LogEntry {
  id: string;
  timestamp: string;
  type: "upload" | "create" | "status";
  method: string;
  url: string;
  status?: number;
  data?: any;
  error?: string;
}

export default function RequestLogger() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  // Intercept fetch requests to log them
  useEffect(() => {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const [url, options] = args;
      const urlString = typeof url === 'string' ? url : url.toString();
      
      // Only log baby-generator API calls
      if (urlString.includes('/api/baby-generator/')) {
        const logId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = new Date().toLocaleTimeString();
        
        let type: "upload" | "create" | "status" = "upload";
        if (urlString.includes('/create')) type = "create";
        else if (urlString.includes('/status/')) type = "status";
        
        const logEntry: LogEntry = {
          id: logId,
          timestamp,
          type,
          method: options?.method || 'GET',
          url: urlString,
        };

        // Add initial log entry
        setLogs(prev => [...prev, logEntry]);

        try {
          const response = await originalFetch(...args);
          const responseClone = response.clone();
          
          // Try to parse response data
          let responseData;
          try {
            responseData = await responseClone.json();
          } catch {
            responseData = await responseClone.text();
          }

          // Update log entry with response
          setLogs(prev => prev.map(log => 
            log.id === logId 
              ? { 
                  ...log, 
                  status: response.status,
                  data: responseData,
                }
              : log
          ));

          return response;
        } catch (error) {
          // Update log entry with error
          setLogs(prev => prev.map(log => 
            log.id === logId 
              ? { 
                  ...log, 
                  error: error instanceof Error ? error.message : 'Unknown error',
                }
              : log
          ));
          throw error;
        }
      }
      
      return originalFetch(...args);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusBadge = (status?: number, error?: string) => {
    if (error) return <Badge variant="destructive">Error</Badge>;
    if (!status) return <Badge variant="secondary">Pending</Badge>;
    if (status >= 200 && status < 300) return <Badge variant="default">Success</Badge>;
    if (status >= 400) return <Badge variant="destructive">Error</Badge>;
    return <Badge variant="secondary">{status}</Badge>;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'upload': return 'text-blue-600';
      case 'create': return 'text-green-600';
      case 'status': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-white shadow-lg"
        >
          <FileText className="h-4 w-4 mr-2" />
          Request Logs ({logs.length})
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 w-96 h-96">
      <Card className="bg-white shadow-xl h-full flex flex-col">
        <CardHeader className="pb-3 flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Request Logs ({logs.length})</CardTitle>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearLogs}
                disabled={logs.length === 0}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 overflow-hidden p-0">
          <div className="h-full px-4 pb-4 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No requests logged yet
              </div>
            ) : (
              <div className="space-y-3">
                {logs.slice().reverse().map((log) => (
                  <div key={log.id} className="border rounded-lg p-3 text-xs">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${getTypeColor(log.type)}`}>
                          {log.type.toUpperCase()}
                        </span>
                        <span className="text-gray-500">{log.timestamp}</span>
                      </div>
                      {getStatusBadge(log.status, log.error)}
                    </div>
                    
                    <div className="space-y-1">
                      <div>
                        <span className="font-medium">{log.method}</span>{' '}
                        <span className="text-gray-600 break-all">
                          {log.url.replace(window.location.origin, '')}
                        </span>
                      </div>
                      
                      {log.error && (
                        <div className="text-red-600 bg-red-50 p-2 rounded">
                          <strong>Error:</strong> {log.error}
                        </div>
                      )}
                      
                      {log.data && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-blue-600 hover:underline">
                            Response Data
                          </summary>
                          <pre className="bg-gray-100 p-2 rounded mt-1 text-xs overflow-x-auto max-h-32">
                            {typeof log.data === 'string' 
                              ? log.data 
                              : JSON.stringify(log.data, null, 2)
                            }
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
