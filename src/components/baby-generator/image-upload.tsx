"use client";

import { useRef, useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, X, User, Heart, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { UploadedImage } from "./index";
import { isValidR2ImageUrl, extractImageUrlFromUploadResponse } from "@/lib/url-utils";

interface ImageUploadProps {
  type: "father" | "mother";
  image: UploadedImage | null;
  onUpload: (image: UploadedImage) => void;
  onRemove: () => void;
  sessionId: string;
}

export default function ImageUpload({ type, image, onUpload, onRemove, sessionId }: ImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const uploadToR2 = async (file: File) => {
    console.log(`🚀 Starting upload for ${type} image:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      sessionId,
    });

    const formData = new FormData();
    formData.append("file", file);
    formData.append("imageType", type);
    formData.append("sessionId", sessionId);

    const response = await fetch("/api/baby-generator/upload", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();

    console.log(`📥 Upload response for ${type} image:`, {
      success: data.code === 0,
      code: data.code,
      message: data.message,
      data: data.data,
    });

    if (data.code !== 0) {
      console.error(`❌ Upload failed for ${type} image:`, data);
      throw new Error(data.message || "Upload failed");
    }

    // 直接从响应中提取 URL，不使用复杂的提取函数
    const uploadUrl = data.data?.uploadResult?.url;

    console.log(`📤 Upload response for ${type} image:`, {
      uploadKey: data.data?.uploadResult?.key,
      uploadUrl: uploadUrl,
      responseCode: data.code,
      responseMessage: data.message,
      hasUploadResult: !!data.data?.uploadResult,
      fullResponse: data,
    });

    if (!uploadUrl) {
      console.error(`❌ No URL found in response for ${type} image:`, {
        dataExists: !!data.data,
        uploadResultExists: !!data.data?.uploadResult,
        urlExists: !!data.data?.uploadResult?.url,
        fullResponse: data,
      });
      toast.error("No image URL received from server");
      throw new Error("No image URL received");
    }

    // 验证 URL 格式（简化验证）
    const isUrlValid = uploadUrl.startsWith('https://') && uploadUrl.includes('r2.dev');

    console.log(`🔍 URL validation for ${type} image:`, {
      url: uploadUrl,
      isValid: isUrlValid,
      startsWithHttps: uploadUrl.startsWith('https://'),
      containsR2Dev: uploadUrl.includes('r2.dev'),
    });

    if (!isUrlValid) {
      console.warn(`⚠️ URL validation failed for ${type} image:`, uploadUrl);
      // 不要抛出错误，只是警告，让用户可以继续使用
      toast.error("Warning: Image URL format may be invalid, but continuing...");
    }

    return data.data;
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    await processFile(file);

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const processFile = async (file: File) => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image size should be less than 10MB");
      return;
    }

    setIsUploading(true);

    try {
      console.log(`🚀 Starting upload process for ${type} image:`, {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      });

      // Create preview URL
      const preview = URL.createObjectURL(file);
      console.log(`🖼️ Preview URL created for ${type} image:`, preview);

      // Upload to R2
      console.log(`☁️ Uploading ${type} image to R2...`);
      const uploadData = await uploadToR2(file);

      console.log(`✅ Upload completed for ${type} image:`, {
        hasUploadResult: !!uploadData.uploadResult,
        uploadKey: uploadData.uploadResult?.key,
        uploadUrl: uploadData.uploadResult?.url,
        uploadFilename: uploadData.uploadResult?.filename,
      });

      // Prepare data for parent component
      const imageData = {
        file,
        preview,
        type,
        uploadResult: uploadData.uploadResult,
      };

      console.log(`📤 Calling onUpload for ${type} image with data:`, imageData);

      // Call parent component handler
      onUpload(imageData);

      console.log(`🎉 Upload process completed successfully for ${type} image`);
      toast.success("Image uploaded successfully!");

    } catch (error) {
      console.error(`❌ Upload failed for ${type} image:`, error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });

      toast.error(`Failed to upload ${type} image. Please try again.`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];

    if (file && file.type.startsWith("image/")) {
      await processFile(file);
    } else {
      toast.error("Please drop an image file");
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const title = type === "father" ? "Father's Photo" : "Mother's Photo";
  const icon = type === "father" ? User : Heart;
  const IconComponent = icon;

  return (
    <Card className="relative">
      <CardHeader className="text-center pb-4">
        <CardTitle className="flex items-center justify-center gap-2 text-lg">
          <IconComponent className="h-5 w-5" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {image ? (
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
              <img
                src={image.preview}
                alt={title}
                className="w-full h-full object-cover"
              />
            </div>
            <Button
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={onRemove}
            >
              <X className="h-4 w-4" />
            </Button>
            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600 truncate">
                {image.file.name}
              </p>
              <p className="text-xs text-gray-400">
                {(image.file.size / 1024 / 1024).toFixed(1)} MB
              </p>
            </div>
          </div>
        ) : (
          <div
            className={`aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors ${
              isUploading ? "pointer-events-none opacity-50" : ""
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => !isUploading && fileInputRef.current?.click()}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-12 w-12 text-primary mb-4 animate-spin" />
                <p className="text-sm text-gray-600 text-center mb-2">
                  Uploading...
                </p>
                <p className="text-xs text-gray-400 text-center">
                  Please wait
                </p>
              </>
            ) : (
              <>
                <Upload className="h-12 w-12 text-gray-400 mb-4" />
                <p className="text-sm text-gray-600 text-center mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-400 text-center">
                  PNG, JPG up to 10MB
                </p>
              </>
            )}
          </div>
        )}

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        {!image && (
          <div className="mt-4 space-y-2 text-xs text-gray-500">
            <p>📸 Tips for best results:</p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>Use a clear, front-facing photo</li>
              <li>Ensure good lighting</li>
              <li>Face should be clearly visible</li>
              <li>Avoid sunglasses or face coverings</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
