"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, EyeOff, Copy, Check } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { UploadedImage, GenerationJob } from "./index";

interface DebugPanelProps {
  fatherImage: UploadedImage | null;
  motherImage: UploadedImage | null;
  generationJob: GenerationJob | null;
  sessionId: string;
}

export default function DebugPanel({ 
  fatherImage, 
  motherImage, 
  generationJob, 
  sessionId 
}: DebugPanelProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [copiedItem, setCopiedItem] = useState<string | null>(null);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(label);
      toast.success(`${label} copied to clipboard!`);
      setTimeout(() => setCopiedItem(null), 2000);
    } catch (error) {
      toast.error("Failed to copy to clipboard");
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-white shadow-lg"
        >
          <Eye className="h-4 w-4 mr-2" />
          Debug Info
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
      <Card className="bg-white shadow-xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Debug Information</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
            >
              <EyeOff className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 text-xs">
          {/* Session Info */}
          <div>
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium">Session ID:</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(sessionId, "Session ID")}
                className="h-6 px-2"
              >
                {copiedItem === "Session ID" ? (
                  <Check className="h-3 w-3" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            </div>
            <code className="text-xs bg-gray-100 p-1 rounded block break-all">
              {sessionId}
            </code>
          </div>

          {/* Father Image Info */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">Father Image:</span>
              <Badge variant={fatherImage ? "default" : "secondary"}>
                {fatherImage ? "Uploaded" : "Not uploaded"}
              </Badge>
            </div>
            {fatherImage && (
              <div className="space-y-1 ml-2">
                <div>File: {fatherImage.file.name}</div>
                <div>Size: {(fatherImage.file.size / 1024 / 1024).toFixed(2)} MB</div>
                {fatherImage.uploadResult && (
                  <>
                    <div className="flex items-center justify-between">
                      <span>Upload Key:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(fatherImage.uploadResult!.key, "Father Upload Key")}
                        className="h-5 px-1"
                      >
                        {copiedItem === "Father Upload Key" ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <code className="text-xs bg-gray-100 p-1 rounded block break-all">
                      {fatherImage.uploadResult.key}
                    </code>
                    <div className="flex items-center justify-between">
                      <span>Image URL:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(fatherImage.uploadResult!.url, "Father Image URL")}
                        className="h-5 px-1"
                      >
                        {copiedItem === "Father Image URL" ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <code className="text-xs bg-green-100 p-1 rounded block break-all">
                      {fatherImage.uploadResult.url}
                    </code>
                    <div className="text-xs text-green-600 mt-1">
                      ✅ 此 URL 将用于 API 调用
                    </div>
                  </>
                )}
                <div>Status: Uploaded to R2</div>
              </div>
            )}
          </div>

          {/* Mother Image Info */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium">Mother Image:</span>
              <Badge variant={motherImage ? "default" : "secondary"}>
                {motherImage ? "Uploaded" : "Not uploaded"}
              </Badge>
            </div>
            {motherImage && (
              <div className="space-y-1 ml-2">
                <div>File: {motherImage.file.name}</div>
                <div>Size: {(motherImage.file.size / 1024 / 1024).toFixed(2)} MB</div>
                {motherImage.uploadResult && (
                  <>
                    <div className="flex items-center justify-between">
                      <span>Upload Key:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(motherImage.uploadResult!.key, "Mother Upload Key")}
                        className="h-5 px-1"
                      >
                        {copiedItem === "Mother Upload Key" ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <code className="text-xs bg-gray-100 p-1 rounded block break-all">
                      {motherImage.uploadResult.key}
                    </code>
                    <div className="flex items-center justify-between">
                      <span>Image URL:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(motherImage.uploadResult!.url, "Mother Image URL")}
                        className="h-5 px-1"
                      >
                        {copiedItem === "Mother Image URL" ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <code className="text-xs bg-green-100 p-1 rounded block break-all">
                      {motherImage.uploadResult.url}
                    </code>
                    <div className="text-xs text-green-600 mt-1">
                      ✅ 此 URL 将用于 API 调用
                    </div>
                  </>
                )}
                <div>Status: Uploaded to R2</div>
              </div>
            )}
          </div>

          {/* Generation Job Info */}
          {generationJob && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">Generation Job:</span>
                <Badge variant={
                  generationJob.status === "completed" ? "default" :
                  generationJob.status === "failed" ? "destructive" :
                  "secondary"
                }>
                  {generationJob.status}
                </Badge>
              </div>
              <div className="space-y-1 ml-2">
                <div className="flex items-center justify-between">
                  <span>Job ID:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generationJob.jobId, "Job ID")}
                    className="h-5 px-1"
                  >
                    {copiedItem === "Job ID" ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </div>
                <code className="text-xs bg-gray-100 p-1 rounded block break-all">
                  {generationJob.jobId}
                </code>
                {generationJob.result && generationJob.result.length > 0 && (
                  <div>
                    <div>Generated Images: {generationJob.result.length}</div>
                    {generationJob.result.map((url, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span>Image {index + 1}:</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(url, `Image ${index + 1} URL`)}
                          className="h-5 px-1"
                        >
                          {copiedItem === `Image ${index + 1} URL` ? (
                            <Check className="h-3 w-3" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
