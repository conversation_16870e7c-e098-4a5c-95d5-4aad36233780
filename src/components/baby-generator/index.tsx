"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Upload, Heart, Sparkles, Download, Share2 } from "lucide-react";
import ImageUpload from "./image-upload";
import GenderSelector from "./gender-selector";
import GenerationResult from "./generation-result";
import DebugPanel from "./debug-panel";
import RequestLogger from "./request-logger";
import { toast } from "sonner";

export interface UploadedImage {
  file: File;
  preview: string;
  type: "father" | "mother";
  uploadResult?: {
    key: string;
    url: string;
    filename: string;
  };
}

export interface GenerationJob {
  jobId: string;
  status: "pending" | "processing" | "completed" | "failed";
  result?: string[];
  error?: string;
}

export default function BabyGenerator() {
  const [fatherImage, setFatherImage] = useState<UploadedImage | null>(null);
  const [motherImage, setMotherImage] = useState<UploadedImage | null>(null);
  const [selectedGender, setSelectedGender] = useState<"babyBoy" | "babyGirl" | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationJob, setGenerationJob] = useState<GenerationJob | null>(null);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  const handleImageUpload = (image: UploadedImage) => {
    console.log(`📸 Image upload handler called for ${image.type}:`, {
      fileName: image.file.name,
      uploadKey: image.uploadResult?.key,
      uploadUrl: image.uploadResult?.url,
      hasUploadResult: !!image.uploadResult,
      hasUrl: !!image.uploadResult?.url,
    });

    // 简化验证：只要有 uploadResult 就接受
    if (!image.uploadResult) {
      console.error(`❌ No upload result for ${image.type} image`);
      toast.error(`Failed to process ${image.type} image upload`);
      return;
    }

    console.log(`✅ Setting ${image.type} image with data:`, {
      key: image.uploadResult.key,
      url: image.uploadResult.url,
      filename: image.uploadResult.filename,
    });

    if (image.type === "father") {
      setFatherImage(image);
    } else {
      setMotherImage(image);
    }

    console.log(`🎯 ${image.type} image state updated successfully`);
  };

  const handleRemoveImage = (type: "father" | "mother") => {
    if (type === "father") {
      setFatherImage(null);
    } else {
      setMotherImage(null);
    }
  };

  const canGenerate = fatherImage && motherImage && selectedGender && !isGenerating &&
                     fatherImage.uploadResult?.url && motherImage.uploadResult?.url;

  const handleGenerate = async () => {
    if (!canGenerate) return;

    console.log("🚀 Starting baby generation process...");

    setIsGenerating(true);
    setGenerationJob(null);

    try {
      // Use the uploaded image URLs from R2
      const fatherImageUrl = fatherImage.uploadResult!.url;
      const motherImageUrl = motherImage.uploadResult!.url;

      // 验证 URL 格式
      console.log("🔗 Extracted image URLs:", {
        fatherImageUrl,
        motherImageUrl,
        fatherUrlValid: fatherImageUrl.startsWith('https://') && fatherImageUrl.includes('r2.dev') && fatherImageUrl.includes('/uploads/'),
        motherUrlValid: motherImageUrl.startsWith('https://') && motherImageUrl.includes('r2.dev') && motherImageUrl.includes('/uploads/'),
      });

      const requestPayload = {
        fatherImageUrl,
        motherImageUrl,
        gender: selectedGender,
        sessionId,
        fatherUploadKey: fatherImage.uploadResult?.key,
        motherUploadKey: motherImage.uploadResult?.key,
      };

      console.log("📤 Sending create request:", requestPayload);

      // Create generation job
      const response = await fetch("/api/baby-generator/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestPayload),
      });

      console.log("📡 Create API response status:", response.status);

      const data = await response.json();
      console.log("📥 Create API response data:", data);

      if (data.code !== 0) {
        console.error("❌ Create request failed:", data);
        throw new Error(data.message || "Failed to create generation job");
      }

      const job: GenerationJob = {
        jobId: data.data.jobId,
        status: "pending",
      };

      console.log("✅ Generation job created:", {
        jobId: job.jobId,
        sessionId: data.data.sessionId,
        userUuid: data.data.userUuid,
      });

      setGenerationJob(job);

      // Start polling for results
      pollJobStatus(job.jobId);

    } catch (error) {
      console.error("❌ Generation failed:", error);
      toast.error("Failed to generate baby photo. Please try again.");
      setIsGenerating(false); // 只在错误时停止生成状态
    }
    // 注意：不在这里设置 setIsGenerating(false)，因为轮询还在进行
  };

  const pollJobStatus = async (jobId: string) => {
    console.log("🔄 Starting status polling for job:", jobId);

    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        console.log(`📊 Polling attempt ${attempts}/${maxAttempts} for job ${jobId}`);

        const response = await fetch(`/api/baby-generator/status/${jobId}`);
        console.log("📡 Status API response status:", response.status);

        const data = await response.json();
        console.log("📥 Status API response data:", data);

        if (data.code !== 0) {
          console.error("❌ Status check failed:", data);
          throw new Error(data.message || "Failed to get job status");
        }

        const status = data.data.status;
        const result = data.data.result;

        console.log("📈 Job status update:", {
          jobId,
          status,
          hasResult: !!result,
          resultCount: result?.length || 0,
          attempt: attempts,
        });

        setGenerationJob(prev => prev ? {
          ...prev,
          status,
          result,
        } : null);

        if (status === "completed") {
          // 确保有结果才停止轮询
          if (result && result.length > 0) {
            console.log("✅ Generation completed successfully with results!", {
              jobId,
              resultUrls: result,
              resultCount: result.length,
            });
            toast.success("Baby photo generated successfully!");
            setIsGenerating(false); // 确保停止生成状态
            return;
          } else {
            console.warn("⚠️ Generation marked as completed but no results yet, continuing to poll...", {
              jobId,
              status,
              result,
              attempt: attempts,
            });
            // 继续轮询，因为虽然状态是 completed 但还没有结果
          }
        }

        if (status === "failed") {
          console.error("❌ Generation failed for job:", jobId);
          toast.error("Generation failed. Please try again.");
          setIsGenerating(false); // 停止生成状态
          return;
        }

        // Continue polling if still processing or if completed but no results yet
        if (status === "processing" || status === "pending" || (status === "completed" && (!result || result.length === 0))) {
          if (attempts < maxAttempts) {
            const reason = status === "completed" ? "completed but no results yet" : status;
            console.log(`⏳ Job ${reason}, will poll again in 1 second... (attempt ${attempts}/${maxAttempts})`);
            setTimeout(poll, 1000); // Poll every 1 second for faster response
          } else {
            console.error("⏰ Polling timeout reached for job:", jobId);
            toast.error("Generation timeout. Please try again.");
            setGenerationJob(prev => prev ? { ...prev, status: "failed" } : null);
            setIsGenerating(false); // 停止生成状态
          }
        } else {
          // 未知状态，也停止轮询
          console.warn("⚠️ Unknown status, stopping polling:", {
            jobId,
            status,
            result,
          });
          setIsGenerating(false);
        }

      } catch (error) {
        console.error("❌ Polling failed:", {
          jobId,
          attempt: attempts,
          error: error instanceof Error ? error.message : error,
        });
        toast.error("Failed to check generation status.");
        setGenerationJob(prev => prev ? { ...prev, status: "failed" } : null);
        setIsGenerating(false); // 停止生成状态
      }
    };

    poll();
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data:image/jpeg;base64, prefix
        const base64 = result.split(",")[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Upload Section */}
      <div className="grid md:grid-cols-2 gap-6">
        <ImageUpload
          type="father"
          image={fatherImage}
          onUpload={handleImageUpload}
          onRemove={() => handleRemoveImage("father")}
          sessionId={sessionId}
        />
        <ImageUpload
          type="mother"
          image={motherImage}
          onUpload={handleImageUpload}
          onRemove={() => handleRemoveImage("mother")}
          sessionId={sessionId}
        />
      </div>

      {/* Gender Selection */}
      <GenderSelector
        selectedGender={selectedGender}
        onGenderSelect={setSelectedGender}
      />

      {/* Generate Button */}
      <div className="text-center">
        <Button
          onClick={handleGenerate}
          disabled={!canGenerate}
          size="lg"
          className="px-8 py-4 text-lg font-semibold"
        >
          {isGenerating ? (
            <>
              <Sparkles className="mr-2 h-5 w-5 animate-spin" />
              Generating Your Baby...
            </>
          ) : (
            <>
              <Heart className="mr-2 h-5 w-5" />
              Meet My Baby
            </>
          )}
        </Button>
        
        {!canGenerate && !isGenerating && (
          <p className="text-sm text-gray-500 mt-2">
            {!fatherImage || !motherImage
              ? "Please upload both parent photos and select baby gender"
              : (!fatherImage.uploadResult?.url || !motherImage.uploadResult?.url)
              ? "Please wait for images to finish uploading"
              : "Please select baby gender"
            }
          </p>
        )}
      </div>

      {/* Generation Result */}
      {generationJob && (
        <GenerationResult job={generationJob} />
      )}

      {/* Debug Panel - only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <>
          <DebugPanel
            fatherImage={fatherImage}
            motherImage={motherImage}
            generationJob={generationJob}
            sessionId={sessionId}
          />
          <RequestLogger />
        </>
      )}
    </div>
  );
}
