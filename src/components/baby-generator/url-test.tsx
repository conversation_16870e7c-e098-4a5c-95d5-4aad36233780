"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { testUrlExtraction } from "@/lib/url-utils";

export default function URLTest() {
  const [testResult, setTestResult] = useState<any>(null);

  const testURLExtraction = () => {
    // 使用工具函数进行测试
    const result = testUrlExtraction();

    setTestResult({
      originalResponse: result.mockResponse,
      extractedUrl: result.extractedUrl,
      isValidUrl: result.isValid,
      urlLength: result.extractedUrl?.length || 0,
      domain: result.extractedUrl ? new URL(result.extractedUrl).hostname : '',
      filename: result.filename,
    });
  };

  const testAPICall = async () => {
    try {
      const testPayload = {
        fatherImageUrl: "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-father.png",
        motherImageUrl: "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-mother.png",
        gender: "babyBoy",
        sessionId: "session_1754368436436_lahiz1xcs",
        fatherUploadKey: "uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-father.png",
        motherUploadKey: "uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754368436436_lahiz1xcs/original/v1-mother.png"
      };

      console.log("🚀 Testing API call with extracted URLs:");
      console.log("📤 Payload:", testPayload);

      const response = await fetch("/api/baby-generator/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testPayload),
      });

      const data = await response.json();
      console.log("📥 API Response:", data);

      setTestResult(prev => ({
        ...prev,
        apiTest: {
          success: data.code === 0,
          response: data,
          payload: testPayload,
        }
      }));

    } catch (error) {
      console.error("❌ API Test failed:", error);
      setTestResult(prev => ({
        ...prev,
        apiTest: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        }
      }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>URL 提取测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testURLExtraction}>
              测试 URL 提取
            </Button>
            <Button onClick={testAPICall} variant="outline">
              测试 API 调用
            </Button>
          </div>

          {testResult && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">URL 提取结果</h3>
                <div className="bg-gray-50 p-4 rounded space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">状态:</span>
                    <Badge variant={testResult.isValidUrl ? "default" : "destructive"}>
                      {testResult.isValidUrl ? "✅ 有效" : "❌ 无效"}
                    </Badge>
                  </div>
                  <div>
                    <span className="font-medium">提取的 URL:</span>
                    <div className="bg-white p-2 rounded mt-1 break-all text-sm">
                      {testResult.extractedUrl}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">域名:</span> {testResult.domain}
                    </div>
                    <div>
                      <span className="font-medium">URL 长度:</span> {testResult.urlLength}
                    </div>
                  </div>
                </div>
              </div>

              {testResult.apiTest && (
                <div>
                  <h3 className="font-semibold mb-2">API 调用结果</h3>
                  <div className="bg-gray-50 p-4 rounded space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">状态:</span>
                      <Badge variant={testResult.apiTest.success ? "default" : "destructive"}>
                        {testResult.apiTest.success ? "✅ 成功" : "❌ 失败"}
                      </Badge>
                    </div>
                    
                    {testResult.apiTest.response && (
                      <details>
                        <summary className="cursor-pointer text-blue-600 hover:underline">
                          查看 API 响应
                        </summary>
                        <pre className="bg-white p-2 rounded mt-2 text-xs overflow-x-auto">
                          {JSON.stringify(testResult.apiTest.response, null, 2)}
                        </pre>
                      </details>
                    )}

                    {testResult.apiTest.error && (
                      <div className="text-red-600 bg-red-50 p-2 rounded">
                        <strong>错误:</strong> {testResult.apiTest.error}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <details>
                <summary className="cursor-pointer text-blue-600 hover:underline">
                  查看完整测试数据
                </summary>
                <pre className="bg-gray-100 p-4 rounded mt-2 text-xs overflow-x-auto">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
