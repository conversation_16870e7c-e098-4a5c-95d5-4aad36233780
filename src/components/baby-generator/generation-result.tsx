"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Share2, RefreshCw, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { GenerationJob } from "./index";
import { toast } from "sonner";

interface GenerationResultProps {
  job: GenerationJob;
}

export default function GenerationResult({ job }: GenerationResultProps) {
  const handleDownload = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `baby-photo-${Date.now()}.jpg`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Image downloaded successfully!");
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Failed to download image");
    }
  };

  const handleShare = async (imageUrl: string) => {
    if (navigator.share) {
      try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const file = new File([blob], "baby-photo.jpg", { type: "image/jpeg" });
        
        await navigator.share({
          title: "My Future Baby",
          text: "Check out what my future baby might look like!",
          files: [file],
        });
      } catch (error) {
        console.error("Share failed:", error);
        fallbackShare(imageUrl);
      }
    } else {
      fallbackShare(imageUrl);
    }
  };

  const fallbackShare = (imageUrl: string) => {
    navigator.clipboard.writeText(imageUrl).then(() => {
      toast.success("Image URL copied to clipboard!");
    }).catch(() => {
      toast.error("Failed to copy URL");
    });
  };

  const getStatusIcon = () => {
    switch (job.status) {
      case "creating":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "running":
      case "processing":
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "failed":
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <RefreshCw className="h-5 w-5 text-gray-500 animate-spin" />;
    }
  };

  const getStatusText = () => {
    switch (job.status) {
      case "creating":
        return "Initializing your baby photo generation...";
      case "pending":
        return "Your request is in the queue...";
      case "running":
      case "processing":
        return "AI is working its magic...";
      case "completed":
        return "Your baby photo is ready!";
      case "failed":
        return "Generation failed. Please try again.";
      default:
        return "Processing your request...";
    }
  };

  const getStatusBadgeVariant = () => {
    switch (job.status) {
      case "creating":
      case "pending":
        return "secondary";
      case "running":
      case "processing":
        return "default";
      case "completed":
        return "default";
      case "failed":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          {getStatusIcon()}
          Generation Result
        </CardTitle>
        <div className="flex justify-center">
          <Badge variant={getStatusBadgeVariant()}>
            {getStatusText()}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {job.status === "completed" && job.result && job.result.length > 0 ? (
          <div className="space-y-6">
            <div className="grid gap-4">
              {job.result.map((imageUrl, index) => (
                <div key={index} className="space-y-4">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 max-w-md mx-auto">
                    <img
                      src={imageUrl}
                      alt={`Generated baby photo ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex justify-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownload(imageUrl)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleShare(imageUrl)}
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center text-sm text-gray-500 space-y-2">
              <p>🎉 Congratulations! Here's what your future baby might look like!</p>
              <p>Remember, this is just a fun prediction based on AI analysis.</p>
            </div>
          </div>
        ) : job.status === "running" || job.status === "processing" || job.status === "pending" || job.status === "creating" ? (
          <div className="text-center py-8">
            <div className="animate-pulse space-y-4">
              <div className="aspect-square rounded-lg bg-gray-200 max-w-md mx-auto"></div>
              <p className="text-gray-500">
                {job.status === "creating"
                  ? "Initializing your request..."
                  : job.status === "pending"
                    ? "Your request is in queue..."
                    : "Generating your baby photo..."
                }
              </p>
              <p className="text-sm text-gray-400">
                This usually takes 30-60 seconds
              </p>
            </div>
          </div>
        ) : job.status === "failed" ? (
          <div className="text-center py-8">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">Generation Failed</p>
            <p className="text-sm text-gray-500">
              Please try again with different photos or check your internet connection.
            </p>
          </div>
        ) : null}
      </CardContent>
    </Card>
  );
}
