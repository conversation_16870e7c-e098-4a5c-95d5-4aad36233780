import {
  updateCreditForOrder,
} from "./credit";
import {
  findOrderByOrderNo,
  OrderStatus,
  updateOrderStatus,
} from "@/models/order";
import { getIsoTimestr } from "@/lib/time";

import Stripe from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { Order } from "@/types/order";
import { PaymentCallbackSession } from "@/types/payment";

/**
 * 处理Stripe支付会话（保持向后兼容）
 */
export async function handleOrderSession(session: Stripe.Checkout.Session) {
  const paymentSession: PaymentCallbackSession = {
    id: session.id,
    payment_status: session.payment_status || 'unpaid',
    customer_email: session.customer_email || undefined,
    customer_details: session.customer_details ? {
      email: session.customer_details.email || undefined
    } : undefined,
    metadata: session.metadata || undefined,
    amount_total: session.amount_total || undefined,
    currency: session.currency || undefined,
  };

  return handlePaymentSession(paymentSession);
}

/**
 * 处理通用支付会话
 * 遵循DRY原则：统一处理不同支付方式的回调
 */
export async function handlePaymentSession(session: PaymentCallbackSession) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== OrderStatus.Created) {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(
      order_no,
      OrderStatus.Paid,
      paid_at,
      paid_email,
      paid_detail
    );

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paid order
        await updateCreditForOrder(order as unknown as Order);
      }

      // update affiliate for paid order
      await updateAffiliateForOrder(order as unknown as Order);
    }

    console.log(
      "handle payment session succeeded: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle payment session failed: ", e);
    throw e;
  }
}
