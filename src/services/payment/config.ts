import { PaymentProvider } from '@/types/payment';

/**
 * 支付配置类
 * 遵循SRP原则：只负责支付配置管理
 */
export class PaymentConfig {
  private static instance: PaymentConfig;
  private provider: PaymentProvider;

  private constructor() {
    // 从环境变量获取支付提供商配置
    const configuredProvider = process.env.PAYMENT_PROVIDER?.toLowerCase();
    
    // 验证配置的有效性
    if (configuredProvider === 'paypal') {
      this.validatePayPalConfig();
      this.provider = PaymentProvider.PAYPAL;
    } else if (configuredProvider === 'stripe' || !configuredProvider) {
      // 默认使用Stripe
      this.validateStripeConfig();
      this.provider = PaymentProvider.STRIPE;
    } else {
      throw new Error(`Unsupported payment provider: ${configuredProvider}`);
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): PaymentConfig {
    if (!PaymentConfig.instance) {
      PaymentConfig.instance = new PaymentConfig();
    }
    return PaymentConfig.instance;
  }

  /**
   * 获取当前支付提供商
   */
  public getProvider(): PaymentProvider {
    return this.provider;
  }

  /**
   * 检查是否为Stripe
   */
  public isStripe(): boolean {
    return this.provider === PaymentProvider.STRIPE;
  }

  /**
   * 检查是否为PayPal
   */
  public isPayPal(): boolean {
    return this.provider === PaymentProvider.PAYPAL;
  }

  /**
   * 验证Stripe配置
   */
  private validateStripeConfig(): void {
    if (!process.env.STRIPE_PRIVATE_KEY || !process.env.STRIPE_PUBLIC_KEY) {
      throw new Error('Stripe configuration is incomplete. Please set STRIPE_PRIVATE_KEY and STRIPE_PUBLIC_KEY');
    }
  }

  /**
   * 验证PayPal配置
   */
  private validatePayPalConfig(): void {
    if (!process.env.PAYPAL_CLIENT_ID || !process.env.PAYPAL_CLIENT_SECRET) {
      throw new Error('PayPal configuration is incomplete. Please set PAYPAL_CLIENT_ID and PAYPAL_CLIENT_SECRET');
    }
  }

  /**
   * 获取Stripe配置
   */
  public getStripeConfig() {
    return {
      publicKey: process.env.STRIPE_PUBLIC_KEY!,
      privateKey: process.env.STRIPE_PRIVATE_KEY!,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
    };
  }

  /**
   * 获取PayPal配置
   */
  public getPayPalConfig() {
    return {
      clientId: process.env.PAYPAL_CLIENT_ID!,
      clientSecret: process.env.PAYPAL_CLIENT_SECRET!,
      environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox', // sandbox or live
      webhookId: process.env.PAYPAL_WEBHOOK_ID,
    };
  }
}
