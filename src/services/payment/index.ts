/**
 * 支付服务模块入口
 * 提供统一的支付服务接口
 */

import { PaymentServiceFactory } from './factory';

export { PaymentServiceFactory } from './factory';
export { PaymentConfig } from './config';
export { StripePaymentService } from './stripe-service';
export { PayPalPaymentService } from './paypal-service';

// 导出类型
export type {
  IPaymentService,
  CreatePaymentSessionParams,
  PaymentSessionResponse,
  PaymentCallbackSession,
  PaymentProvider,
} from '@/types/payment';

// 便捷函数：获取支付服务实例
export const getPaymentService = () => {
  return PaymentServiceFactory.getPaymentService();
};

// 便捷函数：获取当前支付提供商
export const getCurrentPaymentProvider = () => {
  return PaymentServiceFactory.getCurrentProvider();
};
