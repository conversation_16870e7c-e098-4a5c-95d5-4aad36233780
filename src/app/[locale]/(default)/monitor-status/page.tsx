"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Play, Square, RefreshCw, Clock } from "lucide-react";

export default function MonitorStatusPage() {
  const [jobId, setJobId] = useState("5fk1j");
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [statusHistory, setStatusHistory] = useState<any[]>([]);
  const [currentAttempt, setCurrentAttempt] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);

  const startMonitoring = async () => {
    if (!jobId.trim()) {
      alert("请输入 Job ID");
      return;
    }

    setIsMonitoring(true);
    setStatusHistory([]);
    setCurrentAttempt(0);
    setStartTime(new Date());

    let attempts = 0;
    const maxAttempts = 300; // 5 分钟 (300 秒)

    const poll = async () => {
      if (!isMonitoring) return;

      attempts++;
      setCurrentAttempt(attempts);

      try {
        console.log(`🔄 Polling attempt ${attempts} for job ${jobId}`);

        const response = await fetch(`/api/baby-generator/status/${jobId}`);
        const data = await response.json();

        const timestamp = new Date();
        const elapsed = startTime ? Math.round((timestamp.getTime() - startTime.getTime()) / 1000) : 0;

        const statusEntry = {
          attempt: attempts,
          timestamp: timestamp.toLocaleTimeString(),
          elapsed: `${elapsed}s`,
          response: data,
          success: data.code === 0,
        };

        setStatusHistory(prev => [...prev, statusEntry]);

        if (data.code === 0) {
          const { status, result } = data.data;
          
          console.log(`📊 Status: ${status}, Results: ${result ? result.length : 0}`);

          // 应用修复后的逻辑
          if (status === "completed") {
            if (result && result.length > 0) {
              console.log("✅ Generation completed with results! Stopping monitoring.");
              setIsMonitoring(false);
              return;
            } else {
              console.log("⚠️ Completed but no results, continuing to monitor...");
            }
          } else if (status === "failed") {
            console.log("❌ Generation failed! Stopping monitoring.");
            setIsMonitoring(false);
            return;
          }

          // 继续轮询
          if (attempts < maxAttempts) {
            setTimeout(poll, 1000); // 1 秒间隔
          } else {
            console.log("⏰ Monitoring timeout reached");
            setIsMonitoring(false);
          }
        } else {
          console.error("❌ API error:", data.message);
          if (attempts < maxAttempts) {
            setTimeout(poll, 1000);
          } else {
            setIsMonitoring(false);
          }
        }

      } catch (error) {
        console.error("❌ Polling error:", error);
        const statusEntry = {
          attempt: attempts,
          timestamp: new Date().toLocaleTimeString(),
          elapsed: startTime ? `${Math.round((new Date().getTime() - startTime.getTime()) / 1000)}s` : "0s",
          response: { error: error instanceof Error ? error.message : "Unknown error" },
          success: false,
        };

        setStatusHistory(prev => [...prev, statusEntry]);

        if (attempts < maxAttempts) {
          setTimeout(poll, 1000);
        } else {
          setIsMonitoring(false);
        }
      }
    };

    // 开始轮询
    poll();
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">已完成</Badge>;
      case "processing":
        return <Badge className="bg-blue-500">处理中</Badge>;
      case "pending":
        return <Badge className="bg-yellow-500">等待中</Badge>;
      case "failed":
        return <Badge variant="destructive">失败</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">状态监控</h1>
          <p className="text-gray-600">
            实时监控 Baby Generator API 的状态变化
          </p>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>监控控制</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="输入 Job ID (例如: 5fk1j)"
                    value={jobId}
                    onChange={(e) => setJobId(e.target.value)}
                    disabled={isMonitoring}
                  />
                </div>
                
                {!isMonitoring ? (
                  <Button
                    onClick={startMonitoring}
                    className="flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    开始监控
                  </Button>
                ) : (
                  <Button
                    onClick={stopMonitoring}
                    variant="destructive"
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    停止监控
                  </Button>
                )}
              </div>

              {isMonitoring && (
                <div className="mt-4 flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
                    <span className="text-blue-700">
                      正在监控 Job: <strong>{jobId}</strong>
                    </span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-blue-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      尝试: {currentAttempt}
                    </div>
                    <div>
                      间隔: 1秒
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {statusHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>状态历史 ({statusHistory.length} 次请求)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {statusHistory.slice().reverse().map((entry, index) => (
                    <div
                      key={statusHistory.length - index}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        entry.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          #{entry.attempt}
                        </Badge>
                        <span className="text-sm text-gray-600">
                          {entry.timestamp}
                        </span>
                        <span className="text-xs text-gray-500">
                          ({entry.elapsed})
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        {entry.success && entry.response.data ? (
                          <>
                            {getStatusBadge(entry.response.data.status)}
                            <div className="text-sm">
                              结果: {entry.response.data.result ? entry.response.data.result.length : 0} 个
                            </div>
                            {entry.response.data.result && entry.response.data.result.length > 0 && (
                              <Badge className="bg-green-500">🎉 有结果!</Badge>
                            )}
                          </>
                        ) : (
                          <Badge variant="destructive">
                            错误: {entry.response.message || entry.response.error || "未知错误"}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>修复验证</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">✅ 修复后的行为</h4>
                    <ul className="space-y-1 text-green-700">
                      <li>• 1秒轮询一次，响应更快</li>
                      <li>• status="completed" 且有结果才停止</li>
                      <li>• status="completed" 但无结果继续轮询</li>
                      <li>• 按钮在轮询期间保持禁用</li>
                    </ul>
                  </div>
                  
                  <div className="p-4 bg-red-50 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">❌ 修复前的问题</h4>
                    <ul className="space-y-1 text-red-700">
                      <li>• 5秒轮询间隔，响应较慢</li>
                      <li>• status="completed" 就立即停止</li>
                      <li>• 没有结果时会中断轮询</li>
                      <li>• 用户看不到生成的图片</li>
                    </ul>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">🔍 测试说明</h4>
                  <p className="text-blue-700">
                    使用真实的 Job ID (如 5fk1j) 来测试修复后的轮询逻辑。
                    观察系统是否正确等待结果出现后才停止轮询。
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
