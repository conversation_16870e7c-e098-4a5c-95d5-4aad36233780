"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, CheckCircle, XCircle } from "lucide-react";

export default function VerifyPathsPage() {
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const sessionId = `verify_${Date.now()}`;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("imageType", "father");
      formData.append("sessionId", sessionId);

      console.log("🚀 Starting upload verification...");

      const response = await fetch("/api/baby-generator/upload", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();
      
      console.log("📥 Upload response:", data);

      // 验证路径结构
      const key = data.data?.uploadResult?.key;
      const url = data.data?.uploadResult?.url;
      
      const hasAIPrefix = key?.startsWith('ai-baby-generator/');
      const hasUploadsFolder = key?.includes('/uploads/');
      const hasOriginalFolder = key?.includes('/original/');
      const urlMatchesKey = url?.includes(key);

      setUploadResult({
        ...data,
        verification: {
          hasAIPrefix,
          hasUploadsFolder,
          hasOriginalFolder,
          urlMatchesKey,
          allValid: hasAIPrefix && hasUploadsFolder && hasOriginalFolder && urlMatchesKey,
        }
      });

    } catch (error) {
      console.error("❌ Upload failed:", error);
      setUploadResult({ 
        error: error instanceof Error ? error.message : "Unknown error",
        verification: { allValid: false }
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getStatusIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">路径验证测试</h1>
          <p className="text-gray-600">
            验证上传文件是否包含正确的 ai-baby-generator 前缀
          </p>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>上传测试文件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    disabled={isUploading}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {isUploading && (
                    <p className="text-blue-600 mt-2">正在上传...</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {uploadResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  路径验证结果
                  <Badge variant={uploadResult.verification?.allValid ? "default" : "destructive"}>
                    {uploadResult.verification?.allValid ? "✅ 通过" : "❌ 失败"}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {uploadResult.error ? (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 className="text-red-800 font-semibold mb-2">错误</h3>
                      <p className="text-red-700">{uploadResult.error}</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* 路径验证结果 */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-semibold mb-3">路径结构验证</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(uploadResult.verification?.hasAIPrefix)}
                            <span>包含 ai-baby-generator 前缀</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(uploadResult.verification?.hasUploadsFolder)}
                            <span>包含 uploads 文件夹</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(uploadResult.verification?.hasOriginalFolder)}
                            <span>包含 original 文件夹</span>
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(uploadResult.verification?.urlMatchesKey)}
                            <span>URL 与 Key 匹配</span>
                          </div>
                        </div>
                      </div>

                      {/* 实际路径信息 */}
                      {uploadResult.data?.uploadResult && (
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-semibold mb-2">存储 Key</h4>
                            <code className="bg-green-100 p-2 rounded block text-sm break-all">
                              {uploadResult.data.uploadResult.key}
                            </code>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">访问 URL</h4>
                            <div className="bg-blue-100 p-2 rounded">
                              <a 
                                href={uploadResult.data.uploadResult.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline text-sm break-all"
                              >
                                {uploadResult.data.uploadResult.url}
                              </a>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <strong>Session ID:</strong>
                              <div className="bg-gray-100 p-1 rounded mt-1">
                                {uploadResult.data.sessionId}
                              </div>
                            </div>
                            <div>
                              <strong>User UUID:</strong>
                              <div className="bg-gray-100 p-1 rounded mt-1">
                                {uploadResult.data.userUuid}
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 完整响应 */}
                      <details>
                        <summary className="cursor-pointer text-blue-600 hover:underline">
                          查看完整响应
                        </summary>
                        <pre className="bg-gray-100 p-4 rounded mt-2 text-xs overflow-x-auto">
                          {JSON.stringify(uploadResult, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 预期路径示例 */}
          <Card>
            <CardHeader>
              <CardTitle>预期路径格式</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <strong>上传文件 Key:</strong>
                  <code className="bg-gray-100 p-1 rounded ml-2 text-xs">
                    ai-baby-generator/uploads/{`{userUuid}`}/{`{sessionId}`}/original/v1-{`{imageType}`}.{`{ext}`}
                  </code>
                </div>
                <div>
                  <strong>生成文件 Key:</strong>
                  <code className="bg-gray-100 p-1 rounded ml-2 text-xs">
                    ai-baby-generator/generated/{`{userUuid}`}/{`{sessionId}`}/v1/realistic/baby_{`{index}`}.jpg
                  </code>
                </div>
                <div>
                  <strong>完整 URL:</strong>
                  <code className="bg-gray-100 p-1 rounded ml-2 text-xs">
                    https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/{`{key}`}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
