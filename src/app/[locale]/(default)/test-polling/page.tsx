"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Square, RefreshCw } from "lucide-react";

export default function TestPollingPage() {
  const [isPolling, setIsPolling] = useState(false);
  const [pollResults, setPollResults] = useState<any[]>([]);
  const [currentAttempt, setCurrentAttempt] = useState(0);

  const simulatePolling = async (scenario: string) => {
    setIsPolling(true);
    setPollResults([]);
    setCurrentAttempt(0);

    const scenarios = {
      "success": [
        { status: "pending", result: null },
        { status: "processing", result: null },
        { status: "processing", result: null },
        { status: "completed", result: [] }, // 完成但没结果
        { status: "completed", result: [] }, // 继续轮询
        { status: "completed", result: ["https://cdn.futurebaby.ai/file/future-baby-public/result/output_20250805_075545.png"] }, // 有结果，停止
      ],
      "immediate-success": [
        { status: "completed", result: ["https://cdn.futurebaby.ai/file/future-baby-public/result/output_20250805_075545.png"] },
      ],
      "failure": [
        { status: "pending", result: null },
        { status: "processing", result: null },
        { status: "failed", result: null },
      ],
      "completed-no-results": [
        { status: "pending", result: null },
        { status: "processing", result: null },
        { status: "completed", result: [] },
        { status: "completed", result: [] },
        { status: "completed", result: [] },
        { status: "completed", result: [] },
        { status: "completed", result: [] }, // 一直没结果，会继续轮询
      ],
    };

    const responses = scenarios[scenario as keyof typeof scenarios] || scenarios.success;

    for (let i = 0; i < responses.length; i++) {
      if (!isPolling) break; // 如果用户停止了，就退出

      const response = responses[i];
      const attempt = i + 1;
      setCurrentAttempt(attempt);

      console.log(`🔄 Polling attempt ${attempt}:`, response);

      // 模拟轮询逻辑
      const shouldStop = response.status === "completed" && response.result && response.result.length > 0;
      const shouldFail = response.status === "failed";

      const result = {
        attempt,
        timestamp: new Date().toLocaleTimeString(),
        response,
        action: shouldStop ? "STOP - Success" : shouldFail ? "STOP - Failed" : "CONTINUE",
        shouldStopPolling: shouldStop || shouldFail,
      };

      setPollResults(prev => [...prev, result]);

      if (shouldStop) {
        console.log("✅ Polling stopped - Success with results!");
        setIsPolling(false);
        break;
      }

      if (shouldFail) {
        console.log("❌ Polling stopped - Failed!");
        setIsPolling(false);
        break;
      }

      // 等待 2 秒再继续（模拟真实轮询间隔）
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 如果循环结束但还在轮询，说明是超时情况
    if (isPolling) {
      console.log("⏰ Polling would continue (in real app, would timeout eventually)");
      setIsPolling(false);
    }
  };

  const stopPolling = () => {
    setIsPolling(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">轮询逻辑测试</h1>
          <p className="text-gray-600">
            测试修复后的轮询逻辑是否正确等待结果
          </p>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>测试场景</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={() => simulatePolling("success")}
                  disabled={isPolling}
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  正常成功流程
                </Button>
                
                <Button
                  onClick={() => simulatePolling("immediate-success")}
                  disabled={isPolling}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  立即成功
                </Button>
                
                <Button
                  onClick={() => simulatePolling("failure")}
                  disabled={isPolling}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  失败场景
                </Button>
                
                <Button
                  onClick={() => simulatePolling("completed-no-results")}
                  disabled={isPolling}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  <Play className="h-4 w-4" />
                  完成但无结果
                </Button>
              </div>

              {isPolling && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>正在轮询... (尝试 {currentAttempt})</span>
                  </div>
                  <Button
                    onClick={stopPolling}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Square className="h-3 w-3" />
                    停止
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {pollResults.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>轮询结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pollResults.map((result, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          尝试 {result.attempt}
                        </Badge>
                        <span className="text-sm text-gray-600">
                          {result.timestamp}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <div className="text-sm">
                          <strong>状态:</strong> {result.response.status}
                        </div>
                        <div className="text-sm">
                          <strong>结果:</strong> {result.response.result ? `${result.response.result.length} 个` : "无"}
                        </div>
                        <Badge
                          variant={
                            result.action.includes("Success") ? "default" :
                            result.action.includes("Failed") ? "destructive" :
                            "secondary"
                          }
                        >
                          {result.action}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>修复说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">问题 1: 等待正确结果</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>✅ 修复前: status="completed" 就停止轮询</li>
                    <li>✅ 修复后: status="completed" 且 result.length > 0 才停止</li>
                    <li>✅ 如果 completed 但没结果，继续轮询</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">问题 2: 按钮状态管理</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>✅ isGenerating 在整个轮询过程中保持 true</li>
                    <li>✅ 按钮在 isGenerating=true 时被禁用</li>
                    <li>✅ 只有成功/失败/超时时才设置 isGenerating=false</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">测试场景说明</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li><strong>正常成功:</strong> pending → processing → completed(无结果) → completed(有结果) → 停止</li>
                    <li><strong>立即成功:</strong> completed(有结果) → 立即停止</li>
                    <li><strong>失败场景:</strong> pending → processing → failed → 停止</li>
                    <li><strong>完成但无结果:</strong> completed(无结果) → 继续轮询</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
