"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, AlertCircle, CheckCircle } from "lucide-react";

export default function TestUploadDebugPage() {
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const sessionId = `debug_${Date.now()}`;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("imageType", "father");
      formData.append("sessionId", sessionId);

      console.log("🚀 Starting debug upload test...");
      console.log("📋 Request details:", {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        sessionId,
        imageType: "father"
      });

      const response = await fetch("/api/baby-generator/upload", {
        method: "POST",
        body: formData,
      });

      console.log("📡 Response status:", response.status);

      const data = await response.json();
      console.log("📥 Response data:", data);

      // 分析返回的路径
      if (data.code === 0 && data.data?.uploadResult) {
        const { key, url } = data.data.uploadResult;
        
        const analysis = {
          hasAIPrefix: key.startsWith('ai-baby-generator/'),
          hasUploadsFolder: key.includes('/uploads/'),
          hasOriginalFolder: key.includes('/original/'),
          urlIncludesKey: url.includes(key),
          keyLength: key.length,
          urlLength: url.length,
        };

        console.log("🔍 Path analysis:", analysis);

        setUploadResult({
          ...data,
          analysis,
          debugInfo: {
            expectedKeyPrefix: `ai-baby-generator/uploads/`,
            actualKeyPrefix: key.substring(0, key.indexOf('/uploads/') + 9),
            keyStartsCorrectly: key.startsWith('ai-baby-generator/uploads/'),
          }
        });
      } else {
        setUploadResult(data);
      }

    } catch (error) {
      console.error("❌ Upload failed:", error);
      setUploadResult({ 
        error: error instanceof Error ? error.message : "Unknown error"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">上传调试测试</h1>
          <p className="text-gray-600">
            调试上传 API 的路径生成问题
          </p>
        </div>

        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>上传测试文件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    disabled={isUploading}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {isUploading && (
                    <p className="text-blue-600 mt-2">正在上传并分析...</p>
                  )}
                </div>
                
                <div className="text-sm text-gray-600">
                  <p><strong>提示：</strong> 上传后请查看浏览器控制台和服务器终端的详细日志</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {uploadResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  调试结果
                  {uploadResult.analysis?.hasAIPrefix ? (
                    <Badge variant="default" className="bg-green-500">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      路径正确
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      路径错误
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {uploadResult.error ? (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 className="text-red-800 font-semibold mb-2">错误</h3>
                      <p className="text-red-700">{uploadResult.error}</p>
                    </div>
                  ) : uploadResult.data?.uploadResult ? (
                    <div className="space-y-4">
                      {/* 路径分析 */}
                      {uploadResult.analysis && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-3">路径分析</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              {uploadResult.analysis.hasAIPrefix ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span>ai-baby-generator 前缀</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadResult.analysis.hasUploadsFolder ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span>uploads 文件夹</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadResult.analysis.hasOriginalFolder ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span>original 文件夹</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadResult.analysis.urlIncludesKey ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span>URL 包含 Key</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 调试信息 */}
                      {uploadResult.debugInfo && (
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-3">调试信息</h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <strong>期望前缀:</strong>
                              <code className="bg-white p-1 rounded ml-2">
                                {uploadResult.debugInfo.expectedKeyPrefix}
                              </code>
                            </div>
                            <div>
                              <strong>实际前缀:</strong>
                              <code className="bg-white p-1 rounded ml-2">
                                {uploadResult.debugInfo.actualKeyPrefix}
                              </code>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadResult.debugInfo.keyStartsCorrectly ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span>Key 开头正确</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 实际返回值 */}
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold mb-2">返回的 Key</h4>
                          <code className={`p-2 rounded block text-sm break-all ${
                            uploadResult.analysis?.hasAIPrefix ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {uploadResult.data.uploadResult.key}
                          </code>
                        </div>
                        
                        <div>
                          <h4 className="font-semibold mb-2">返回的 URL</h4>
                          <div className={`p-2 rounded ${
                            uploadResult.analysis?.hasAIPrefix ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            <a 
                              href={uploadResult.data.uploadResult.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline text-sm break-all"
                            >
                              {uploadResult.data.uploadResult.url}
                            </a>
                          </div>
                        </div>
                      </div>

                      {/* 完整响应 */}
                      <details>
                        <summary className="cursor-pointer text-blue-600 hover:underline">
                          查看完整响应
                        </summary>
                        <pre className="bg-gray-100 p-4 rounded mt-2 text-xs overflow-x-auto">
                          {JSON.stringify(uploadResult, null, 2)}
                        </pre>
                      </details>
                    </div>
                  ) : (
                    <div className="text-gray-500">
                      无效的响应格式
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 说明 */}
          <Card>
            <CardHeader>
              <CardTitle>调试说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <strong>期望的 Key 格式:</strong>
                  <code className="bg-gray-100 p-1 rounded ml-2 text-xs">
                    ai-baby-generator/uploads/{`{userUuid}`}/{`{sessionId}`}/original/v1-{`{imageType}`}.{`{ext}`}
                  </code>
                </div>
                <div>
                  <strong>期望的 URL 格式:</strong>
                  <code className="bg-gray-100 p-1 rounded ml-2 text-xs">
                    https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/...
                  </code>
                </div>
                <div className="mt-4">
                  <strong>检查项目:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Key 是否以 ai-baby-generator/ 开头</li>
                    <li>URL 是否包含完整的 Key 路径</li>
                    <li>文件夹结构是否正确</li>
                    <li>服务器日志中的详细信息</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
