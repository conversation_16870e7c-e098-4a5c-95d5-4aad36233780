"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload } from "lucide-react";

export default function TestUploadPage() {
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [sessionId] = useState(() => `test_${Date.now()}`);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("imageType", "father");
      formData.append("sessionId", sessionId);

      console.log("🚀 Starting test upload...");

      const response = await fetch("/api/baby-generator/upload", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();
      
      console.log("📥 Upload response:", data);
      setUploadResult(data);

    } catch (error) {
      console.error("❌ Upload failed:", error);
      setUploadResult({ error: error instanceof Error ? error.message : "Unknown error" });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">Test Upload Functionality</h1>
        <p className="text-gray-600">
          This page is for testing the image upload to Cloudflare R2
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Upload Test Image</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Session ID: <code className="bg-gray-100 px-2 py-1 rounded">{sessionId}</code>
                </label>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {isUploading && (
                  <p className="text-blue-600 mt-2">Uploading...</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {uploadResult && (
          <Card>
            <CardHeader>
              <CardTitle>Upload Result</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {uploadResult.error ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-red-800 font-semibold mb-2">Error</h3>
                    <p className="text-red-700">{uploadResult.error}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h3 className="text-green-800 font-semibold mb-2">Success!</h3>
                      <p className="text-green-700">Upload completed successfully</p>
                    </div>

                    <div className="grid gap-4">
                      <div>
                        <h4 className="font-semibold mb-2">Response Code</h4>
                        <code className="bg-gray-100 p-2 rounded block">
                          {uploadResult.code}
                        </code>
                      </div>

                      {uploadResult.data?.uploadResult && (
                        <div>
                          <h4 className="font-semibold mb-2">Upload Details</h4>
                          <div className="bg-gray-50 p-4 rounded space-y-2">
                            <div>
                              <strong>Key:</strong>
                              <code className="bg-white p-1 rounded ml-2 text-sm break-all">
                                {uploadResult.data.uploadResult.key}
                              </code>
                            </div>
                            <div>
                              <strong>URL:</strong>
                              <a 
                                href={uploadResult.data.uploadResult.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline ml-2 break-all"
                              >
                                {uploadResult.data.uploadResult.url}
                              </a>
                            </div>
                            <div>
                              <strong>Filename:</strong>
                              <code className="bg-white p-1 rounded ml-2 text-sm">
                                {uploadResult.data.uploadResult.filename}
                              </code>
                            </div>
                          </div>
                        </div>
                      )}

                      {uploadResult.data?.base64Image && (
                        <div>
                          <h4 className="font-semibold mb-2">Base64 Data</h4>
                          <div className="bg-gray-50 p-4 rounded">
                            <p>Length: {uploadResult.data.base64Image.length} characters</p>
                            <details className="mt-2">
                              <summary className="cursor-pointer text-blue-600 hover:underline">
                                Show Base64 Data (click to expand)
                              </summary>
                              <code className="bg-white p-2 rounded block mt-2 text-xs break-all max-h-32 overflow-y-auto">
                                {uploadResult.data.base64Image}
                              </code>
                            </details>
                          </div>
                        </div>
                      )}

                      <div>
                        <h4 className="font-semibold mb-2">Full Response</h4>
                        <details>
                          <summary className="cursor-pointer text-blue-600 hover:underline">
                            Show Raw JSON (click to expand)
                          </summary>
                          <pre className="bg-gray-100 p-4 rounded mt-2 text-xs overflow-x-auto">
                            {JSON.stringify(uploadResult, null, 2)}
                          </pre>
                        </details>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
