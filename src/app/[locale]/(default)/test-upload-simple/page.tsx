"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, CheckCircle, AlertCircle } from "lucide-react";

export default function TestUploadSimplePage() {
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const sessionId = `simple_test_${Date.now()}`;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("imageType", "father");
      formData.append("sessionId", sessionId);

      console.log("🚀 Starting simple upload test...");

      const response = await fetch("/api/baby-generator/upload", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();
      console.log("📥 Upload response:", data);

      setUploadResult({
        success: data.code === 0,
        data: data,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error("❌ Upload failed:", error);
      setUploadResult({ 
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">简单上传测试</h1>
          <p className="text-gray-600">
            测试修复后的上传功能是否正常工作
          </p>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>上传文件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {isUploading && (
                  <p className="text-blue-600 mt-2">正在上传...</p>
                )}
              </div>
            </CardContent>
          </Card>

          {uploadResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  上传结果
                  {uploadResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {uploadResult.success ? (
                    <div className="space-y-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 className="text-green-800 font-semibold mb-2">✅ 上传成功！</h3>
                        <p className="text-green-700">文件已成功上传到 Cloudflare R2</p>
                      </div>

                      {uploadResult.data?.data?.uploadResult && (
                        <div className="space-y-3">
                          <div>
                            <h4 className="font-semibold mb-2">存储信息</h4>
                            <div className="bg-gray-50 p-3 rounded text-sm space-y-2">
                              <div>
                                <strong>Key:</strong>
                                <div className="bg-white p-2 rounded mt-1 break-all font-mono text-xs">
                                  {uploadResult.data.data.uploadResult.key}
                                </div>
                              </div>
                              <div>
                                <strong>文件名:</strong>
                                <span className="ml-2">{uploadResult.data.data.uploadResult.filename}</span>
                              </div>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold mb-2">访问地址</h4>
                            <div className="bg-blue-50 p-3 rounded">
                              <a 
                                href={uploadResult.data.data.uploadResult.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline text-sm break-all"
                              >
                                {uploadResult.data.data.uploadResult.url}
                              </a>
                            </div>
                          </div>

                          <div>
                            <h4 className="font-semibold mb-2">会话信息</h4>
                            <div className="bg-gray-50 p-3 rounded text-sm">
                              <div><strong>Session ID:</strong> {uploadResult.data.data.sessionId}</div>
                              <div><strong>User UUID:</strong> {uploadResult.data.data.userUuid}</div>
                              <div><strong>Image Type:</strong> {uploadResult.data.data.imageType}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 className="text-red-800 font-semibold mb-2">❌ 上传失败</h3>
                      <p className="text-red-700">
                        {uploadResult.error || "未知错误"}
                      </p>
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    测试时间: {uploadResult.timestamp}
                  </div>

                  <details>
                    <summary className="cursor-pointer text-blue-600 hover:underline">
                      查看完整响应
                    </summary>
                    <pre className="bg-gray-100 p-4 rounded mt-2 text-xs overflow-x-auto">
                      {JSON.stringify(uploadResult, null, 2)}
                    </pre>
                  </details>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>测试说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <p><strong>这个测试验证：</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>文件是否能成功上传到 Cloudflare R2</li>
                  <li>返回的 URL 是否格式正确</li>
                  <li>URL 是否可以访问</li>
                  <li>前端验证逻辑是否正常工作</li>
                </ul>
                <p className="mt-4"><strong>预期结果：</strong></p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>上传成功，显示绿色成功消息</li>
                  <li>返回的 URL 包含 ai-baby-generator 前缀</li>
                  <li>点击 URL 可以访问上传的图片</li>
                  <li>不再显示 "Invalid image URL received from server" 错误</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
