import { Metadata } from "next";
import URLTest from "@/components/baby-generator/url-test";

export const metadata: Metadata = {
  title: "URL 提取测试 - AI Baby Generator",
  description: "测试从上传响应中提取图片 URL 的功能",
};

export default function TestURLPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            URL 提取测试
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            测试从上传响应中正确提取图片 URL 并用于 API 调用
          </p>
        </div>
        
        <URLTest />
      </div>
    </div>
  );
}
