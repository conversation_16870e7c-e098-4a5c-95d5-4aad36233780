import { handlePaymentSession } from "@/services/order";
import { redirect } from "next/navigation";
import { getPaymentService } from "@/services/payment";

export default async function ({
  params,
  searchParams,
}: {
  params: Promise<{ session_id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  let redirectLocale = "en";

  try {
    const { session_id } = await params;
    const urlParams = await searchParams;

    // 检查是否是PayPal返回
    if (urlParams.provider === 'paypal') {
      // PayPal返回，session_id就是PayPal的订单ID
      console.log('Processing PayPal payment success, order ID:', session_id);
      const paymentService = getPaymentService();
      const session = await paymentService.getPaymentSession(session_id);
      await handlePaymentSession(session);
    } else {
      // Stripe或其他支付方式，使用原有逻辑
      const paymentService = getPaymentService();
      const session = await paymentService.getPaymentSession(session_id);
      await handlePaymentSession(session);
    }
  } catch (e) {
    console.error("Payment success page error:", e);
    redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
  }

  redirect({
    href: process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/",
    locale: redirectLocale,
  });
}
