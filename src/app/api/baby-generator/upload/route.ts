import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";

export async function POST(req: Request) {
  try {
    console.log("📤 Baby Generator Upload API called");

    const formData = await req.formData();
    const file = formData.get("file") as File;
    const imageType = formData.get("imageType") as string; // "father" or "mother"
    const sessionId = formData.get("sessionId") as string;

    console.log("📋 Upload request details:", {
      hasFile: !!file,
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      imageType,
      sessionId,
    });

    if (!file || !imageType || !sessionId) {
      console.error("❌ Missing required parameters:", { file: !!file, imageType, sessionId });
      return respErr("Missing required parameters");
    }

    if (!["father", "mother"].includes(imageType)) {
      console.error("❌ Invalid imageType:", imageType);
      return respErr("Invalid imageType. Must be 'father' or 'mother'");
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      console.error("❌ Invalid file type:", file.type);
      return respErr("File must be an image");
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      console.error("❌ File too large:", file.size);
      return respErr("File size must be less than 10MB");
    }

    // Get user UUID (optional, can be empty for anonymous users)
    const userUuid = await getUserUuid() || "anonymous";
    console.log("👤 User UUID:", userUuid);
    
    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Generate storage key following the path convention
    // Note: ai-baby-generator prefix is already included in STORAGE_ENDPOINT and STORAGE_BUCKET
    const version = 1; // Start with v1, can be incremented for multiple uploads
    const fileExtension = file.type === "image/png" ? "png" : "jpg";
    const key = `uploads/${userUuid}/${sessionId}/original/v${version}-${imageType}.${fileExtension}`;

    // Upload to Cloudflare R2
    console.log("☁️ Uploading to Cloudflare R2:");
    console.log("   📁 Generated key:", key);
    console.log("   📄 File type:", file.type);
    console.log("   📏 File size:", buffer.length);

    const storage = newStorage();
    const uploadResult = await storage.uploadFile({
      body: buffer,
      key,
      contentType: file.type,
      disposition: "inline",
    });

    console.log("✅ Upload successful:");
    console.log("   📁 Returned key:", uploadResult.key);
    console.log("   🔗 Returned URL:", uploadResult.url);
    console.log("   📄 Returned filename:", uploadResult.filename);

    // Verify the returned key matches our generated key
    const keyMatches = uploadResult.key === key;
    console.log("   🔍 Key matches:", keyMatches);

    if (!keyMatches) {
      console.warn("⚠️  WARNING: Returned key doesn't match generated key!");
      console.warn("   Generated:", key);
      console.warn("   Returned: ", uploadResult.key);
    }

    const responseData = {
      uploadResult: {
        key: uploadResult.key,
        url: uploadResult.url,
        filename: uploadResult.filename,
      },
      sessionId,
      userUuid,
      imageType,
    };

    console.log("📤 Returning upload response:", responseData);

    return respData(responseData);

  } catch (error) {
    console.error("Image upload failed:", error);
    return respErr("Failed to upload image");
  }
}
