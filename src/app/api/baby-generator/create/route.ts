import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUuid } from "@/lib/hash";
import { newStorage } from "@/lib/storage";

const API_KEY = process.env.BABY_GENERATOR_API_KEY || "2af893ab-6a1f-4557-8ec0-081b275371a1";
const API_BASE_URL = "https://api.maxstudio.ai";

export async function POST(req: Request) {
  try {
    console.log("🚀 Baby Generator Create API called");

    const requestBody = await req.json();
    const {
      fatherImageUrl,
      motherImageUrl,
      gender,
      sessionId,
      fatherUploadKey,
      motherUploadKey
    } = requestBody;

    console.log("📋 Create request details:", {
      fatherImageUrl,
      motherImageUrl,
      gender,
      sessionId,
      fatherUploadKey,
      motherUploadKey,
    });

    if (!fatherImageUrl || !motherImageUrl || !gender || !sessionId) {
      console.error("❌ Missing required parameters:", {
        hasFatherImageUrl: !!fatherImageUrl,
        hasMotherImageUrl: !!motherImageUrl,
        gender,
        sessionId,
      });
      return respErr("Missing required parameters");
    }

    if (!["babyBoy", "babyGirl"].includes(gender)) {
      console.error("❌ Invalid gender:", gender);
      return respErr("Invalid gender. Must be 'babyBoy' or 'babyGirl'");
    }

    // Get user UUID for tracking (optional)
    const userUuid = await getUserUuid();

    console.log("👤 User details:", {
      userUuid: userUuid || 'anonymous',
      sessionId,
      gender,
    });

    console.log("📁 Upload paths:", {
      fatherUploadKey,
      motherUploadKey,
    });

    // Prepare API request payload using image URLs
    const apiPayload = {
      fatherImage: fatherImageUrl,
      motherImage: motherImageUrl,
      gender,
    };

    console.log("🌐 Calling MaxStudio AI API:", {
      url: `${API_BASE_URL}/baby-generator`,
      apiKey: `${API_KEY.substring(0, 8)}...`,
      payload: apiPayload,
    });

    // Call the baby generator API
    const response = await fetch(`${API_BASE_URL}/baby-generator`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": API_KEY,
      },
      body: JSON.stringify(apiPayload),
    });

    console.log("📡 MaxStudio AI API response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Baby generator API error:", {
        status: response.status,
        statusText: response.statusText,
        errorText,
      });
      return respErr(`API request failed: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log("📥 MaxStudio AI API response data:", data);

    if (!data.jobId) {
      console.error("❌ Invalid API response - missing jobId:", data);
      return respErr("Invalid response from baby generator API");
    }

    console.log("✅ Baby generation job created successfully:", {
      jobId: data.jobId,
      sessionId,
      userUuid: userUuid || "anonymous",
    });

    const responseData = {
      jobId: data.jobId,
      sessionId,
      userUuid: userUuid || "anonymous",
      fatherUploadKey,
      motherUploadKey,
    };

    console.log("📤 Returning create response:", responseData);

    return respData(responseData);

  } catch (error) {
    console.error("❌ Baby generation creation failed:", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return respErr("Failed to create baby generation job");
  }
}
