import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";

export async function POST(req: Request) {
  try {
    const { userUuid, sessionId, imageUrls, jobId } = await req.json();

    if (!userUuid || !sessionId || !imageUrls || !Array.isArray(imageUrls)) {
      return respErr("Missing required parameters");
    }

    console.log(`Background task: Storing ${imageUrls.length} generated images for user ${userUuid}, session ${sessionId}`);

    const storage = newStorage();
    const results = [];

    for (let i = 0; i < imageUrls.length; i++) {
      try {
        const imageUrl = imageUrls[i];
        // Note: ai-baby-generator prefix is already included in STORAGE_ENDPOINT and STORAGE_BUCKET
        const key = `generated/${userUuid}/${sessionId}/v1/realistic/baby_${i}.jpg`;
        
        const result = await storage.downloadAndUpload({
          url: imageUrl,
          key,
          contentType: "image/jpeg",
          disposition: "inline",
        });

        results.push({
          index: i,
          success: true,
          key: result.key,
          url: result.url,
        });

        console.log(`✅ Background stored image ${i}: ${result.key}`);
      } catch (error) {
        console.error(`❌ Background storage failed for image ${i}:`, error);
        results.push({
          index: i,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`Background task completed: ${successCount}/${imageUrls.length} images stored successfully`);

    return respData({
      jobId,
      userUuid,
      sessionId,
      totalImages: imageUrls.length,
      successCount,
      results,
    });

  } catch (error) {
    console.error("Background image storage task failed:", error);
    return respErr("Background storage task failed");
  }
}
