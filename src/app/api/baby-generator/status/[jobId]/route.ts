import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { newStorage } from "@/lib/storage";
import { getUuid } from "@/lib/hash";

const API_KEY = process.env.BABY_GENERATOR_API_KEY || "2af893ab-6a1f-4557-8ec0-081b275371a1";
const API_BASE_URL = "https://api.maxstudio.ai";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params;

    console.log("📊 Baby Generator Status API called for job:", jobId);

    if (!jobId) {
      console.error("❌ Missing job ID");
      return respErr("Job ID is required");
    }

    console.log("🌐 Calling MaxStudio AI status API:", {
      url: `${API_BASE_URL}/baby-generator/${jobId}`,
      apiKey: `${API_KEY.substring(0, 8)}...`,
    });

    // Call the baby generator status API
    const response = await fetch(`${API_BASE_URL}/baby-generator/${jobId}`, {
      method: "GET",
      headers: {
        "x-api-key": API_KEY,
      },
    });

    console.log("📡 MaxStudio AI status response:", {
      jobId,
      status: response.status,
      statusText: response.statusText,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Baby generator status API error:", {
        jobId,
        status: response.status,
        statusText: response.statusText,
        errorText,
      });
      return respErr(`API request failed: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log("📥 MaxStudio AI status data:", {
      jobId,
      status: data.status,
      hasResult: !!data.result,
      resultCount: data.result?.length || 0,
      result: data.result,
    });

    // If generation is completed and we have results, store them asynchronously
    if (data.status === "completed" && data.result && data.result.length > 0) {
      console.log("✅ Generation completed, starting async image storage:", {
        jobId,
        imageCount: data.result.length,
      });

      const userUuid = await getUserUuid();

      // Store generated images asynchronously (don't wait for completion)
      storeGeneratedImages(userUuid || "anonymous", jobId, data.result).catch(error => {
        console.error("❌ Failed to store generated images:", {
          jobId,
          error: error instanceof Error ? error.message : error,
        });
      });
    }

    const responseData = {
      status: data.status,
      result: data.result || [],
    };

    console.log("📤 Returning status response:", {
      jobId,
      ...responseData,
    });

    return respData(responseData);

  } catch (error) {
    console.error("❌ Baby generation status check failed:", {
      jobId,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return respErr("Failed to check baby generation status");
  }
}

async function storeGeneratedImages(userUuid: string, jobId: string, imageUrls: string[]) {
  const storage = newStorage();

  // Use jobId as sessionId for consistency
  const sessionId = jobId;

  console.log(`Starting async storage of ${imageUrls.length} generated images for user ${userUuid}, job ${jobId}`);

  for (let i = 0; i < imageUrls.length; i++) {
    try {
      const imageUrl = imageUrls[i];

      // Use the downloadAndUpload method from storage
      // Note: ai-baby-generator prefix is already included in STORAGE_ENDPOINT and STORAGE_BUCKET
      const key = `generated/${userUuid}/${sessionId}/v1/realistic/baby_${i}.jpg`;

      const result = await storage.downloadAndUpload({
        url: imageUrl,
        key,
        contentType: "image/jpeg",
        disposition: "inline",
      });

      console.log(`✅ Stored generated image ${i} for user ${userUuid}: ${result.key}`);
    } catch (error) {
      console.error(`❌ Failed to store generated image ${i}:`, error);
    }
  }

  console.log(`Completed async storage for job ${jobId}`);
}
