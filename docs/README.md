# ShipAny Template One - 文档中心

欢迎来到 ShipAny Template One 的文档中心！这里包含了项目的完整文档和指南。

## 📚 文档导航

### 🚀 快速开始
- **[项目说明文档](PROJECT_README.md)** - 项目概述、技术栈、快速启动指南
- **[支付系统完整指南](PAYMENT_SYSTEM.md)** - 双支付系统（Stripe + PayPal）的完整配置和使用指南

## 🔄 文档更新记录

### v2.7.0 (2025-07-12)
- 🎉 **新增**：[支付系统完整指南](PAYMENT_SYSTEM.md) - 整合所有支付相关文档
- 🔄 **更新**：[项目说明文档](PROJECT_README.md) - 更新为双支付系统描述
- 📝 **新增**：本文档索引，提供清晰的导航结构

### v2.7.0 (2025-07-12)
- 🔄 **新增**：[项目说明文档](PROJECT_README.md) - 分析代码形成文档

## 💡 文档贡献

如果您发现文档中的错误或需要补充内容，欢迎：

1. 提交 Issue 描述问题
2. 提交 Pull Request 修复文档
3. 联系项目维护者

## 🔗 相关链接

- **项目仓库**：[GitHub Repository](https://github.com/shipanyai/shipany-template-one)
- **Stripe 文档**：[https://docs.stripe.com/](https://docs.stripe.com/)
- **PayPal 开发者**：[https://developer.paypal.com/](https://developer.paypal.com/)
- **Next.js 文档**：[https://nextjs.org/docs](https://nextjs.org/docs)

---

*最后更新：2025-07-12*
