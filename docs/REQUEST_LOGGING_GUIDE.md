# 🔍 Baby Generator 请求日志查看指南

## 概述

为了帮助调试和监控 AI Baby Generator 的请求流程，我们添加了详细的日志记录功能。你可以通过多种方式查看请求和响应的详细信息。

## 📊 查看方式

### 1. 浏览器开发者工具（推荐）

**步骤：**
1. 打开 `http://localhost:3000/generator`
2. 按 `F12` 打开开发者工具
3. 切换到 **Console** 标签页查看详细日志
4. 切换到 **Network** 标签页查看网络请求

**Console 日志示例：**
```
🚀 Starting baby generation process...
📤 Sending create request: {
  fatherImage: "[123456 characters]",
  motherImage: "[123456 characters]",
  gender: "babyBoy",
  sessionId: "session_1234567890_abc123",
  fatherUploadKey: "uploads/anonymous/session_1234567890_abc123/original/v1-father.jpg",
  motherUploadKey: "uploads/anonymous/session_1234567890_abc123/original/v1-mother.jpg"
}
📡 Create API response status: 200
📥 Create API response data: {
  code: 0,
  message: "success",
  data: {
    jobId: "job_abc123def456",
    sessionId: "session_1234567890_abc123",
    userUuid: "anonymous"
  }
}
✅ Generation job created: {
  jobId: "job_abc123def456",
  sessionId: "session_1234567890_abc123",
  userUuid: "anonymous"
}
```

### 2. 页面内置请求日志器（开发环境）

**步骤：**
1. 在开发环境下访问 `http://localhost:3000/generator`
2. 页面左下角会显示 **"Request Logs (0)"** 按钮
3. 点击按钮查看实时请求日志
4. 日志会自动记录所有 baby-generator API 请求

**功能特点：**
- 📝 实时记录所有 API 请求
- 🎨 彩色标记不同类型的请求（上传/创建/状态）
- 📊 显示请求状态和响应数据
- 🗑️ 支持清空日志
- 📋 可展开查看完整响应数据

### 3. 调试信息面板（开发环境）

**步骤：**
1. 在开发环境下访问 `http://localhost:3000/generator`
2. 页面右下角会显示 **"Debug Info"** 按钮
3. 点击查看上传文件和生成任务的详细信息

**显示内容：**
- 📋 Session ID
- 📸 父母照片上传信息
- 🔗 Cloudflare R2 存储路径
- 📊 生成任务状态和结果

### 4. 服务器端日志

**查看方式：**
在运行 `pnpm dev` 的终端中查看服务器日志

**日志示例：**

**上传 API 日志：**
```
📤 Baby Generator Upload API called
📋 Upload request details: {
  hasFile: true,
  fileName: "father.jpg",
  fileSize: 2048576,
  fileType: "image/jpeg",
  imageType: "father",
  sessionId: "session_1234567890_abc123"
}
👤 User UUID: anonymous
☁️ Uploading to Cloudflare R2: uploads/anonymous/session_1234567890_abc123/original/v1-father.jpg
✅ Upload successful: {
  key: "uploads/anonymous/session_1234567890_abc123/original/v1-father.jpg",
  url: "https://your-r2-domain.com/...",
  filename: "v1-father.jpg"
}
🔄 Base64 conversion completed, length: 123456
📤 Returning upload response: { ... }
```

**创建任务 API 日志：**
```
🚀 Baby Generator Create API called
📋 Create request details: {
  hasFatherImage: true,
  fatherImageLength: 123456,
  hasMotherImage: true,
  motherImageLength: 123456,
  gender: "babyBoy",
  sessionId: "session_1234567890_abc123",
  fatherUploadKey: "uploads/anonymous/session_1234567890_abc123/original/v1-father.jpg",
  motherUploadKey: "uploads/anonymous/session_1234567890_abc123/original/v1-mother.jpg"
}
👤 User details: {
  userUuid: "anonymous",
  sessionId: "session_1234567890_abc123",
  gender: "babyBoy"
}
🌐 Calling MaxStudio AI API: {
  url: "https://api.maxstudio.ai/baby-generator",
  apiKey: "6a4322f6...",
  payload: {
    fatherImage: "[123456 characters]",
    motherImage: "[123456 characters]",
    gender: "babyBoy"
  }
}
📡 MaxStudio AI API response status: 200
📥 MaxStudio AI API response data: {
  jobId: "job_abc123def456"
}
✅ Baby generation job created successfully: {
  jobId: "job_abc123def456",
  sessionId: "session_1234567890_abc123",
  userUuid: "anonymous"
}
📤 Returning create response: { ... }
```

**状态查询 API 日志：**
```
📊 Baby Generator Status API called for job: job_abc123def456
🌐 Calling MaxStudio AI status API: {
  url: "https://api.maxstudio.ai/baby-generator/job_abc123def456",
  apiKey: "6a4322f6..."
}
📡 MaxStudio AI status response: {
  jobId: "job_abc123def456",
  status: 200,
  statusText: "OK"
}
📥 MaxStudio AI status data: {
  jobId: "job_abc123def456",
  status: "completed",
  hasResult: true,
  resultCount: 1,
  result: ["https://generated-image-url.jpg"]
}
✅ Generation completed, starting async image storage: {
  jobId: "job_abc123def456",
  imageCount: 1
}
📤 Returning status response: {
  jobId: "job_abc123def456",
  status: "completed",
  result: ["https://generated-image-url.jpg"]
}
```

## 🔧 日志级别说明

| 图标 | 含义 | 描述 |
|------|------|------|
| 🚀 | 开始 | 操作开始 |
| 📤 | 发送 | 发送请求或返回响应 |
| 📥 | 接收 | 接收响应数据 |
| 📋 | 详情 | 请求详细信息 |
| 📡 | 网络 | 网络请求状态 |
| 👤 | 用户 | 用户相关信息 |
| 📁 | 文件 | 文件路径信息 |
| ☁️ | 云存储 | Cloudflare R2 操作 |
| 🔄 | 处理 | 数据处理过程 |
| ✅ | 成功 | 操作成功 |
| ❌ | 错误 | 操作失败 |
| ⏳ | 等待 | 等待状态 |
| 📊 | 状态 | 状态查询 |

## 🐛 调试技巧

### 1. 检查上传流程
- 查看 `📤 Baby Generator Upload API called` 日志
- 确认文件信息和 R2 上传结果
- 检查 base64 转换是否成功

### 2. 检查生成请求
- 查看 `🚀 Baby Generator Create API called` 日志
- 确认请求参数完整性
- 检查 MaxStudio AI API 调用结果

### 3. 检查状态轮询
- 查看 `📊 Baby Generator Status API called` 日志
- 监控轮询频率和状态变化
- 确认结果返回和存储过程

### 4. 常见问题排查

**上传失败：**
- 检查文件大小和格式
- 确认 Cloudflare R2 配置
- 查看存储权限设置

**生成失败：**
- 检查 API Key 是否正确
- 确认 base64 数据完整性
- 查看 MaxStudio AI API 错误信息

**状态查询失败：**
- 检查 jobId 是否正确
- 确认网络连接稳定
- 查看轮询超时设置

## 📝 日志文件位置

开发环境下，所有日志都会输出到：
- **浏览器控制台**：前端日志
- **终端**：服务器端日志
- **页面内置日志器**：实时请求日志

生产环境建议配置专门的日志收集系统。

---

通过这些详细的日志，你可以完整地追踪整个 Baby Generator 的请求流程，快速定位和解决问题！🎉
