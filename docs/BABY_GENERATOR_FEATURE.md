# AI Baby Generator Feature Documentation

## 功能概述

AI Baby Generator 是一个基于人工智能的宝宝预测功能，用户可以上传父母双方的照片，AI 会分析面部特征并生成预测的宝宝照片。

## 功能特点

- 🤖 **先进的 AI 技术**: 使用 MaxStudio AI 的专业宝宝生成模型
- 📸 **简单易用**: 只需上传两张照片即可生成
- 🔒 **隐私保护**: 照片处理完成后立即删除，不存储用户数据
- ⚡ **快速生成**: 通常在 30-60 秒内完成生成
- 📱 **响应式设计**: 支持桌面端和移动端
- 💾 **结果保存**: 支持下载和分享生成的宝宝照片

## 技术实现

### 前端组件

1. **BabyGenerator** (`src/components/baby-generator/index.tsx`)
   - 主要的生成器组件，管理整个生成流程
   - 处理图片上传、性别选择、生成请求和结果展示

2. **ImageUpload** (`src/components/baby-generator/image-upload.tsx`)
   - 图片上传组件，支持拖拽上传
   - 图片预览和文件验证功能

3. **GenderSelector** (`src/components/baby-generator/gender-selector.tsx`)
   - 性别选择组件，支持选择男宝宝或女宝宝

4. **GenerationResult** (`src/components/baby-generator/generation-result.tsx`)
   - 结果展示组件，显示生成状态和最终结果
   - 支持下载和分享功能

### API 路由

1. **图片上传** (`/api/baby-generator/upload`)
   - 📤 **直接上传**: 用户上传的图片直接存储到 Cloudflare R2
   - 🔄 **实时处理**: 同时返回 base64 编码用于 AI 处理
   - ⚡ **性能优化**: 减少生成时的等待时间

2. **创建生成任务** (`/api/baby-generator/create`)
   - 接收已上传图片的 base64 数据和性别选择
   - 调用 MaxStudio AI API 创建生成任务
   - 记录上传的图片路径信息

3. **查询生成状态** (`/api/baby-generator/status/[jobId]`)
   - 轮询查询生成任务状态
   - 返回生成结果或错误信息
   - 🔄 **异步存储**: 生成完成后异步上传宝宝照片到 R2

4. **后台图片存储** (`/api/baby-generator/store-images`)
   - 🔧 **可选接口**: 用于手动触发图片存储
   - 📊 **批量处理**: 支持批量下载和存储生成的图片

### 文件存储路径规范

```
uploads/{userId}/{sessionId}/original/v{version}-{imageType}.jpg
generated/{userId}/{sessionId}/v{version}/{style}/baby.jpg
```

**注意**: `ai-baby-generator` 前缀已经包含在 Cloudflare R2 的存储桶配置中，不需要在代码中重复添加。

- `userId`: 用户唯一 ID
- `sessionId`: 生成任务会话 ID
- `version`: 版本号（v1, v2...）
- `imageType`: 图片类型（father 或 mother）
- `style`: 生成风格（realistic, cartoon 等）

## 环境配置

在 `.env` 文件中添加以下配置：

```env
# Baby Generator API Configuration
BABY_GENERATOR_API_KEY=your_api_key_here
```

## 使用流程

1. **访问生成器页面**: 用户点击"Meet My Baby"按钮跳转到 `/generator` 页面
2. **上传照片**: 分别上传父亲和母亲的照片
   - 📤 **优化**: 照片直接上传到 Cloudflare R2，减少等待时间
   - 🔄 **实时反馈**: 显示上传进度和状态
3. **选择性别**: 选择希望生成的宝宝性别（男宝宝或女宝宝）
4. **开始生成**: 点击"Meet My Baby"按钮开始生成
   - ⚡ **快速启动**: 使用已上传的图片，无需重新处理
5. **等待结果**: 系统显示生成进度，通常需要 30-60 秒
6. **查看结果**: 生成完成后显示宝宝照片
7. **下载分享**: 用户可以下载或分享生成的照片
8. **后台存储**: 生成的宝宝照片异步上传到 Cloudflare R2

## API 集成

### MaxStudio AI Baby Generator API

**创建生成任务**:
```bash
curl -X POST https://api.maxstudio.ai/baby-generator \
  -H "Content-Type: application/json" \
  -H "x-api-key: 2af893ab-6a1f-4557-8ec0-081b275371a1" \
  -d '{
    "fatherImage": "https://your-r2-domain.com/path/to/father.jpg",
    "motherImage": "https://your-r2-domain.com/path/to/mother.jpg",
    "gender": "babyBoy"
  }'
```

**查询任务状态**:
```bash
curl -X GET https://api.maxstudio.ai/baby-generator/{job_id} \
  -H "x-api-key: 2af893ab-6a1f-4557-8ec0-081b275371a1"
```

**重要变更**:
- ✅ API 接受图片 URL 而不是 base64 数据
- ✅ 使用 Cloudflare R2 的公开 URL
- ✅ 更快的请求处理（无需传输大量 base64 数据）

## 错误处理

- 图片格式验证：只支持常见图片格式（JPG, PNG 等）
- 文件大小限制：最大 10MB
- API 错误处理：网络错误、API 限制等
- 超时处理：生成超时后提示用户重试

## 隐私保护

- 所有上传的照片在处理完成后立即删除
- 不存储用户的个人照片
- 生成的结果图片可选择性存储（仅限已登录用户）
- 符合 GDPR 和其他隐私法规要求

## 性能优化

- 图片压缩：上传前自动压缩大尺寸图片
- 异步处理：使用轮询机制避免长时间等待
- 缓存机制：合理缓存 API 响应
- 错误重试：网络错误时自动重试

## 未来扩展

- 支持更多生成风格（卡通、艺术风格等）
- 批量生成多个变体
- 生成历史记录管理
- 社交分享功能增强
- 移动端 App 支持
