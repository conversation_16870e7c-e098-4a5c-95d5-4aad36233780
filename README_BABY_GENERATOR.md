# 🍼 AI Baby Generator - 使用指南

## 功能简介

AI Baby Generator 是一个基于人工智能的宝宝预测功能，让用户可以上传父母双方的照片，AI 会分析面部特征并生成预测的宝宝照片。

## 🚀 快速开始

### 1. 环境配置

在 `.env` 文件中添加以下配置：

```env
# 更新项目名称
NEXT_PUBLIC_PROJECT_NAME="AI Baby Generator"

# Baby Generator API 配置
BABY_GENERATOR_API_KEY=6a4322f6-c5d1-4875-9a5a-a8f228dbf918
```

### 2. 启动项目

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

### 3. 访问功能

- 打开浏览器访问 `http://localhost:3000`
- 点击落地页上的 "Meet My Baby" 按钮
- 或直接访问 `http://localhost:3000/generator`

## 📱 使用流程

1. **上传照片** ⚡ **优化后**
   - 点击上传区域或拖拽照片到指定区域
   - 分别上传父亲和母亲的照片
   - 支持 JPG、PNG 格式，最大 10MB
   - 📤 **直接上传到 R2**: 照片立即上传到 Cloudflare R2，减少等待时间
   - 🔄 **实时反馈**: 显示上传进度和状态

2. **选择性别**
   - 选择希望生成的宝宝性别（男宝宝或女宝宝）

3. **开始生成** ⚡ **更快启动**
   - 点击 "Meet My Baby" 按钮开始生成
   - 使用已上传的图片，无需重新处理
   - 等待 30-60 秒完成处理

4. **查看结果**
   - 生成完成后查看宝宝照片
   - 可以下载或分享生成的照片
   - 🔄 **后台存储**: 生成的宝宝照片自动异步上传到 R2

## 🧪 测试功能

运行测试脚本验证 API 是否正常工作：

```bash
pnpm test:baby-generator
```

## 📁 文件结构

```
src/
├── app/
│   ├── [locale]/(default)/generator/
│   │   └── page.tsx                    # 生成器页面
│   └── api/baby-generator/
│       ├── upload/route.ts             # 📤 图片上传到 R2 API
│       ├── create/route.ts             # 创建生成任务 API
│       ├── status/[jobId]/route.ts     # 查询任务状态 API
│       └── store-images/route.ts       # 🔄 后台图片存储 API
├── components/baby-generator/
│   ├── index.tsx                       # 主要生成器组件
│   ├── image-upload.tsx                # 📤 优化的图片上传组件
│   ├── gender-selector.tsx             # 性别选择组件
│   └── generation-result.tsx           # 结果展示组件
└── i18n/pages/landing/
    └── en.json                         # 更新了落地页内容
```

## 🔧 技术特点

- **React 19** + **Next.js 15** 现代化框架
- **TypeScript** 类型安全
- **Tailwind CSS** + **Shadcn/UI** 现代化 UI
- **Sonner** Toast 通知
- **拖拽上传** 支持
- **响应式设计** 移动端友好
- **隐私保护** 照片处理后立即删除

## 🛡️ 隐私保护

- ✅ 所有上传的照片在处理完成后立即删除
- ✅ 不存储用户的个人照片
- ✅ 生成的结果图片可选择性存储（仅限已登录用户）
- ✅ 符合 GDPR 和其他隐私法规要求

## 🔗 API 集成

使用 MaxStudio AI 的 Baby Generator API：

- **API 文档**: https://api.maxstudio.ai/baby-generator
- **支持的性别**: `babyBoy`, `babyGirl`
- **图片格式**: Base64 编码的图片数据
- **处理时间**: 通常 30-60 秒

## 📊 存储路径规范

如果用户已登录，系统会按照以下路径存储图片：

```
ai-baby-generator/uploads/{userId}/{sessionId}/original/v{version}-{imageType}.jpg
ai-baby-generator/generated/{userId}/{sessionId}/v{version}/{style}/baby.jpg
```

## 🚨 错误处理

- 图片格式验证
- 文件大小限制（最大 10MB）
- API 错误处理
- 网络超时处理
- 用户友好的错误提示

## 🎨 自定义配置

### 更新品牌信息

编辑 `src/i18n/pages/landing/en.json` 文件：

```json
{
  "header": {
    "brand": {
      "title": "AI Baby Generator"
    }
  }
}
```

### 修改 API 配置

在 `.env` 文件中更新：

```env
BABY_GENERATOR_API_KEY=your_api_key_here
```

## 📈 性能优化

- 图片自动压缩
- 异步处理和轮询
- 错误重试机制
- 缓存优化

## 🔮 未来扩展

- [ ] 支持更多生成风格
- [ ] 批量生成多个变体
- [ ] 生成历史记录
- [ ] 社交分享功能
- [ ] 移动端 App

## 📞 技术支持

如有问题，请查看：

1. **文档**: `docs/BABY_GENERATOR_FEATURE.md`
2. **测试脚本**: `scripts/test-baby-generator.js`
3. **API 日志**: 检查浏览器控制台和服务器日志

---

🎉 **恭喜！** 您已成功集成 AI Baby Generator 功能！
