# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "AI Baby Generator"

# -----------------------------------------------------------------------------
# Baby Generator API Configuration
# -----------------------------------------------------------------------------
BABY_GENERATOR_API_KEY = "2af893ab-6a1f-4557-8ec0-081b275371a1"
# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase DATABASE_URL
DATABASE_URL = "postgresql://postgres.rqhsxqtxqftadnpuguqx:<EMAIL>:6543/postgres"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "L7lSmDo+NO5Dvgi5ZcjEi2DqGumW+/s+uehHx8EFFSU="
AUTH_URL = "http://localhost:3000/api/auth"
AUTH_TRUST_HOST = true

# disable auth if needed
# NEXT_PUBLIC_AUTH_ENABLED = "false"

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "************-f8fkbanvg8jarn25t35pjk64nli7qs9d.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "************-f8fkbanvg8jarn25t35pjk64nli7qs9d.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
# https://plausible.io/
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""

# -----------------------------------------------------------------------------
# Payment Configuration
# Choose payment provider: 'stripe' or 'paypal' (default: stripe)
# Only one provider can be active at a time
# -----------------------------------------------------------------------------
PAYMENT_PROVIDER = "paypal"

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# Required when PAYMENT_PROVIDER = "stripe"
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# -----------------------------------------------------------------------------
# Payment with PayPal
# https://developer.paypal.com/docs/api/overview/
# Required when PAYMENT_PROVIDER = "paypal"
# -----------------------------------------------------------------------------
PAYPAL_CLIENT_ID = "AVvv9oWdOrBP5N5kd-Pw2lDDTvZjzTZyhLt4es9eiyD2V-RL3xMW-Q1JuS_EJjR3wwsVL3sEvdgDr3Nj"
PAYPAL_CLIENT_SECRET = "EJfkUMpwiR3uG2G2uQFJDG5LEW8LEsP5sdbsXmdgsqFDhRQQfXkinyWgQEBL-z5PxXmgp37eQTbSX2AM"
PAYPAL_ENVIRONMENT = "sandbox"  # sandbox or live
# Optional: For webhook verification (can be left empty for basic setup)
PAYPAL_WEBHOOK_ID = ""
PAYPAL_WEBHOOK_SECRET = ""

# Payment URLs (used by both providers)
NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,mailto:<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://007508f4192f2b34f5e6ca07338b197f.r2.cloudflarestorage.com/ai-baby-generator"
STORAGE_REGION = "auto"
STORAGE_ACCESS_KEY = "1ed16a57a15fcb8d36b4db28e32677e9"
STORAGE_SECRET_KEY = "72559d9d70907cbcfc2afca299f85e645c2c8a15965bba96416b3c02a350eba5"
STORAGE_BUCKET = "ai-baby-generator"
STORAGE_DOMAIN = "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev"

# Google Adsence Code
# https://adsense.com/
NEXT_PUBLIC_GOOGLE_ADCODE = ""
