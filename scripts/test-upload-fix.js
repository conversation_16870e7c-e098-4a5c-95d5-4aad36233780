#!/usr/bin/env node

/**
 * Quick test to verify the upload path fix
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

async function testUploadPathFix() {
  console.log('🔧 Testing Upload Path Fix\n');

  try {
    // Test the upload endpoint with a mock request to see the expected path structure
    console.log('📤 Testing upload path generation...');
    
    const sessionId = `test_${Date.now()}`;
    const mockUserUuid = 'test-user-uuid';
    
    // Expected path after fix
    const expectedKey = `ai-baby-generator/uploads/${mockUserUuid}/${sessionId}/original/v1-father.jpg`;
    const expectedUrl = `https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/${expectedKey}`;

    console.log('✅ Expected results after fix:');
    console.log(`📁 Expected Key: ${expectedKey}`);
    console.log(`🔗 Expected URL: ${expectedUrl}`);

    // Verify the key structure
    const hasAIPrefix = expectedKey.startsWith('ai-baby-generator/');
    const hasUploadsFolder = expectedKey.includes('/uploads/');
    const hasOriginalFolder = expectedKey.includes('/original/');
    const hasVersionPrefix = expectedKey.includes('/v1-');

    console.log('\n🔍 Path Structure Validation:');
    console.log(`✅ Has ai-baby-generator prefix: ${hasAIPrefix}`);
    console.log(`✅ Has uploads folder: ${hasUploadsFolder}`);
    console.log(`✅ Has original folder: ${hasOriginalFolder}`);
    console.log(`✅ Has version prefix: ${hasVersionPrefix}`);

    const allValid = hasAIPrefix && hasUploadsFolder && hasOriginalFolder && hasVersionPrefix;
    console.log(`\n🎯 Path structure is: ${allValid ? '✅ CORRECT' : '❌ INCORRECT'}`);

    // Test the actual endpoint (if server is running)
    console.log('\n🧪 Testing actual upload endpoint...');
    
    try {
      const response = await fetch(`${API_BASE_URL}/api/baby-generator/upload`, {
        method: 'POST',
        // Empty body to test endpoint response
      });

      console.log(`📡 Upload endpoint status: ${response.status}`);
      
      if (response.status === 400) {
        console.log('✅ Upload endpoint is accessible (returns 400 for missing parameters as expected)');
        
        const errorData = await response.json();
        console.log('📋 Error response:', errorData);
      } else {
        console.log(`ℹ️  Unexpected status: ${response.status}`);
      }

    } catch (error) {
      console.log('ℹ️  Server not running or endpoint not accessible');
      console.log('   Start the server with: pnpm dev');
    }

    console.log('\n📝 Summary:');
    console.log('- Upload API should now generate keys with ai-baby-generator/ prefix');
    console.log('- URLs should include the full path with prefix');
    console.log('- Test with actual file upload to verify the fix');

    return {
      expectedKey,
      expectedUrl,
      pathValidation: {
        hasAIPrefix,
        hasUploadsFolder,
        hasOriginalFolder,
        hasVersionPrefix,
        allValid,
      }
    };

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return null;
  }
}

async function main() {
  console.log('🍼 Upload Path Fix Verification\n');
  console.log(`🌐 API Base URL: ${API_BASE_URL}\n`);

  const result = await testUploadPathFix();

  if (result && result.pathValidation.allValid) {
    console.log('\n🎉 Path structure validation passed!');
    console.log('✅ The upload API should now generate correct paths with ai-baby-generator prefix.');
    console.log('\n🚀 Next steps:');
    console.log('1. Start the server: pnpm dev');
    console.log('2. Test actual upload: http://localhost:3000/verify-paths');
    console.log('3. Or test the main feature: http://localhost:3000/generator');
  } else {
    console.log('\n❌ Path structure validation failed.');
    console.log('Please check the upload API implementation.');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testUploadPathFix
};
