#!/usr/bin/env node

/**
 * Real API test script using actual image URLs
 * This script tests the exact same flow as your successful curl command
 */

const API_KEY = '2af893ab-6a1f-4557-8ec0-081b275371a1';
const API_BASE_URL = 'https://api.maxstudio.ai';

// Use the same URLs from your successful test
const FATHER_IMAGE_URL = 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-father.jpg';
const MOTHER_IMAGE_URL = 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-mother.png';

async function testRealAPI() {
  console.log('🧪 Testing Real MaxStudio AI API...\n');

  try {
    // Step 1: Create generation job
    console.log('📤 Creating generation job...');
    console.log(`👨 Father image: ${FATHER_IMAGE_URL}`);
    console.log(`👩 Mother image: ${MOTHER_IMAGE_URL}`);
    
    const createResponse = await fetch(`${API_BASE_URL}/baby-generator`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
      },
      body: JSON.stringify({
        fatherImage: FATHER_IMAGE_URL,
        motherImage: MOTHER_IMAGE_URL,
        gender: 'babyBoy'
      }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`Create request failed: ${createResponse.status} - ${errorText}`);
    }

    const createData = await createResponse.json();
    console.log('✅ Job created successfully!');
    console.log(`📋 Job ID: ${createData.jobId}\n`);

    // Step 2: Poll for status
    const jobId = createData.jobId;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes

    console.log('🔄 Polling for results...');

    while (attempts < maxAttempts) {
      attempts++;
      console.log(`📊 Attempt ${attempts}/${maxAttempts}...`);

      const statusResponse = await fetch(`${API_BASE_URL}/baby-generator/${jobId}`, {
        method: 'GET',
        headers: {
          'x-api-key': API_KEY,
        },
      });

      if (!statusResponse.ok) {
        const errorText = await statusResponse.text();
        throw new Error(`Status request failed: ${statusResponse.status} - ${errorText}`);
      }

      const statusData = await statusResponse.json();
      console.log(`📈 Status: ${statusData.status}`);

      if (statusData.status === 'completed') {
        console.log('🎉 Generation completed!');
        console.log(`🖼️  Generated images: ${statusData.result.length}`);
        statusData.result.forEach((url, index) => {
          console.log(`   ${index + 1}. ${url}`);
        });
        return statusData;
      }

      if (statusData.status === 'failed') {
        throw new Error('Generation failed');
      }

      // Wait 5 seconds before next poll
      if (attempts < maxAttempts) {
        console.log('⏳ Waiting 5 seconds...\n');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }

    throw new Error('Polling timeout - generation took too long');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

async function testOurAPI() {
  console.log('\n🔧 Testing Our API Endpoints...\n');

  const LOCAL_API_BASE = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
  const sessionId = `test_${Date.now()}`;

  try {
    // Test our create endpoint
    console.log('📤 Testing our create endpoint...');
    
    const response = await fetch(`${LOCAL_API_BASE}/api/baby-generator/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fatherImageUrl: FATHER_IMAGE_URL,
        motherImageUrl: MOTHER_IMAGE_URL,
        gender: 'babyBoy',
        sessionId,
        fatherUploadKey: 'uploads/test/session/v1-father.jpg',
        motherUploadKey: 'uploads/test/session/v1-mother.jpg'
      }),
    });

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`Our API failed: ${data.message}`);
    }

    console.log('✅ Our create API works!');
    console.log(`📋 Job ID: ${data.data.jobId}`);

    // Test our status endpoint
    console.log('\n📊 Testing our status endpoint...');
    
    const statusResponse = await fetch(`${LOCAL_API_BASE}/api/baby-generator/status/${data.data.jobId}`);
    const statusData = await statusResponse.json();
    
    if (statusData.code !== 0) {
      throw new Error(`Our status API failed: ${statusData.message}`);
    }

    console.log('✅ Our status API works!');
    console.log(`📈 Status: ${statusData.data.status}`);

  } catch (error) {
    console.error('❌ Our API test failed:', error.message);
  }
}

async function main() {
  console.log('🍼 Real API Test Suite\n');
  console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
  console.log(`🌐 API Base URL: ${API_BASE_URL}\n`);

  // Test the real MaxStudio API first
  const result = await testRealAPI();
  
  // Test our API endpoints
  await testOurAPI();

  console.log('\n✨ All tests completed!');
  
  if (result && result.result) {
    console.log('\n🎨 Generated baby images:');
    result.result.forEach((url, index) => {
      console.log(`${index + 1}. ${url}`);
    });
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testRealAPI,
  testOurAPI
};
