#!/usr/bin/env node

/**
 * Test URL validation logic with the actual response
 */

// Import the URL validation functions (simulate them here for testing)
function isValidR2ImageUrl(url) {
  if (!url || typeof url !== 'string') {
    console.warn('URL validation failed: URL is empty or not a string');
    return false;
  }

  try {
    const urlObj = new URL(url);
    
    // 检查协议
    if (urlObj.protocol !== 'https:') {
      console.warn('URL validation failed: Protocol is not HTTPS');
      return false;
    }

    // 检查域名是否包含 r2.dev
    if (!urlObj.hostname.includes('r2.dev')) {
      console.warn('URL validation failed: Hostname does not contain r2.dev');
      return false;
    }

    // 检查路径是否包含图片文件扩展名
    const pathname = urlObj.pathname.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));

    if (!hasImageExtension) {
      console.warn('URL validation failed: No valid image extension found');
      return false;
    }

    // 检查路径是否包含 uploads 或 generated 文件夹（表示是我们的文件）
    const hasValidPath = pathname.includes('/uploads/') || pathname.includes('/generated/');

    if (!hasValidPath) {
      console.warn('URL validation failed: Path does not contain /uploads/ or /generated/');
      return false;
    }

    console.log('✅ URL validation passed:', url);
    return true;
  } catch (error) {
    console.error('URL validation error:', error);
    return false;
  }
}

function extractImageUrlFromUploadResponse(response) {
  try {
    if (response?.data?.uploadResult?.url) {
      return response.data.uploadResult.url;
    }
    return null;
  } catch (error) {
    console.error('Failed to extract image URL:', error);
    return null;
  }
}

async function testUrlValidation() {
  console.log('🔍 Testing URL Validation Logic\n');

  // The actual response you provided
  const actualResponse = {
    "code": 0,
    "message": "ok",
    "data": {
      "uploadResult": {
        "key": "uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754379527056_sd7urvccu/original/v1-father.png",
        "url": "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754379527056_sd7urvccu/original/v1-father.png",
        "filename": "v1-father.png"
      },
      "sessionId": "session_1754379527056_sd7urvccu",
      "userUuid": "0eb3db92-83d7-4725-b084-70570b7373d7",
      "imageType": "father"
    }
  };

  console.log('📥 Testing with actual response:');
  console.log(JSON.stringify(actualResponse, null, 2));

  // Test URL extraction
  console.log('\n🔗 Testing URL extraction...');
  const extractedUrl = extractImageUrlFromUploadResponse(actualResponse);
  console.log('Extracted URL:', extractedUrl);

  if (!extractedUrl) {
    console.error('❌ Failed to extract URL');
    return;
  }

  // Test URL validation
  console.log('\n✅ Testing URL validation...');
  const isValid = isValidR2ImageUrl(extractedUrl);
  console.log('Validation result:', isValid);

  // Detailed analysis
  console.log('\n📊 Detailed URL Analysis:');
  try {
    const urlObj = new URL(extractedUrl);
    console.log('Protocol:', urlObj.protocol);
    console.log('Hostname:', urlObj.hostname);
    console.log('Pathname:', urlObj.pathname);
    
    const pathname = urlObj.pathname.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));
    const hasValidPath = pathname.includes('/uploads/') || pathname.includes('/generated/');
    
    console.log('Has image extension:', hasImageExtension);
    console.log('Has valid path (/uploads/ or /generated/):', hasValidPath);
    console.log('Contains r2.dev:', urlObj.hostname.includes('r2.dev'));
    console.log('Is HTTPS:', urlObj.protocol === 'https:');

    // Test if URL is accessible (just structure check)
    console.log('\n🌐 URL Structure Check:');
    console.log('Full URL:', extractedUrl);
    console.log('Expected to work:', isValid ? '✅ YES' : '❌ NO');

  } catch (error) {
    console.error('Error analyzing URL:', error);
  }

  return {
    extractedUrl,
    isValid,
    response: actualResponse
  };
}

async function main() {
  console.log('🍼 URL Validation Test\n');
  console.log('Testing the URL validation logic with your actual upload response.\n');

  const result = await testUrlValidation();

  if (result && result.isValid) {
    console.log('\n🎉 URL validation test passed!');
    console.log('✅ The URL should be accepted by the frontend.');
  } else {
    console.log('\n❌ URL validation test failed.');
    console.log('The frontend will reject this URL.');
    
    if (result && result.extractedUrl) {
      console.log('\n🔧 Debugging suggestions:');
      console.log('1. Check if the URL format matches expectations');
      console.log('2. Verify the validation logic is not too strict');
      console.log('3. Consider updating the validation rules');
    }
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testUrlValidation,
  isValidR2ImageUrl,
  extractImageUrlFromUploadResponse
};
