#!/usr/bin/env node

/**
 * Test the error fix with the actual response data
 */

// Simulate the actual response you provided
const actualResponse = {
  "code": 0,
  "message": "ok",
  "data": {
    "uploadResult": {
      "key": "uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754380007449_7wj0qd4s2/original/v1-father.png",
      "url": "https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/0eb3db92-83d7-4725-b084-70570b7373d7/session_1754380007449_7wj0qd4s2/original/v1-father.png",
      "filename": "v1-father.png"
    },
    "sessionId": "session_1754380007449_7wj0qd4s2",
    "userUuid": "0eb3db92-83d7-4725-b084-70570b7373d7",
    "imageType": "father"
  }
};

function testErrorFix() {
  console.log('🔧 Testing Error Fix with Actual Response\n');

  console.log('📥 Actual response from your upload:');
  console.log(JSON.stringify(actualResponse, null, 2));

  // Test the new simplified extraction logic
  console.log('\n🔗 Testing simplified URL extraction...');
  
  const data = actualResponse;
  const uploadUrl = data.data?.uploadResult?.url;
  
  console.log('Extracted URL:', uploadUrl);
  console.log('URL exists:', !!uploadUrl);

  if (!uploadUrl) {
    console.error('❌ No URL found in response:', {
      dataExists: !!data.data,
      uploadResultExists: !!data.data?.uploadResult,
      urlExists: !!data.data?.uploadResult?.url,
    });
    return false;
  }

  // Test simplified validation
  console.log('\n✅ Testing simplified URL validation...');
  const isUrlValid = uploadUrl.startsWith('https://') && uploadUrl.includes('r2.dev');
  
  console.log('Validation results:', {
    url: uploadUrl,
    isValid: isUrlValid,
    startsWithHttps: uploadUrl.startsWith('https://'),
    containsR2Dev: uploadUrl.includes('r2.dev'),
  });

  // Test the upload result structure
  console.log('\n📦 Testing upload result structure...');
  const uploadResult = data.data?.uploadResult;
  
  console.log('Upload result validation:', {
    hasUploadResult: !!uploadResult,
    hasKey: !!uploadResult?.key,
    hasUrl: !!uploadResult?.url,
    hasFilename: !!uploadResult?.filename,
    key: uploadResult?.key,
    url: uploadResult?.url,
    filename: uploadResult?.filename,
  });

  // Test what the frontend should receive
  console.log('\n🎯 Testing frontend data structure...');
  const imageData = {
    file: { name: 'test-father.png' }, // Mock file
    preview: 'blob:http://localhost:3000/test-preview',
    type: 'father',
    uploadResult: uploadResult,
  };

  console.log('Frontend image data:', {
    hasFile: !!imageData.file,
    hasPreview: !!imageData.preview,
    hasType: !!imageData.type,
    hasUploadResult: !!imageData.uploadResult,
    uploadResultValid: !!(imageData.uploadResult && imageData.uploadResult.url),
  });

  const shouldPass = !!(uploadUrl && isUrlValid && uploadResult && uploadResult.url);
  console.log(`\n🎯 Overall test result: ${shouldPass ? '✅ SHOULD PASS' : '❌ WILL FAIL'}`);

  return shouldPass;
}

function main() {
  console.log('🍼 Error Fix Test\n');
  console.log('Testing the simplified error handling logic with your actual response.\n');

  const result = testErrorFix();

  if (result) {
    console.log('\n🎉 Error fix test passed!');
    console.log('✅ The frontend should now accept this upload response.');
    console.log('✅ No more "No image URL received from server" errors.');
    console.log('✅ No more "Failed to upload image" errors.');
    
    console.log('\n🚀 Next steps:');
    console.log('1. Start the server: pnpm dev');
    console.log('2. Test upload: http://localhost:3000/generator');
    console.log('3. Upload the same image and verify no errors appear');
  } else {
    console.log('\n❌ Error fix test failed.');
    console.log('There may still be issues with the error handling logic.');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testErrorFix
};
