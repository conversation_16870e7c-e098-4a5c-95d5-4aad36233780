#!/usr/bin/env node

/**
 * Test script for Baby Generator API
 * Usage: node scripts/test-baby-generator.js
 */

const fs = require('fs');
const path = require('path');

const API_BASE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
const API_KEY = process.env.BABY_GENERATOR_API_KEY || '2af893ab-6a1f-4557-8ec0-081b275371a1';

// Sample base64 encoded test images (1x1 pixel images for testing)
const SAMPLE_FATHER_IMAGE = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';
const SAMPLE_MOTHER_IMAGE = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';

async function testUploadAndCreate() {
  console.log('🧪 Testing Baby Generator API with R2 Upload...\n');

  try {
    const sessionId = `test_session_${Date.now()}`;

    // Test upload functionality (simulated)
    console.log('📤 Testing image upload flow...');
    console.log(`🔗 Session ID: ${sessionId}`);

    // In a real test, you would upload actual files
    // For now, we'll simulate the upload result (ai-baby-generator prefix is handled by storage config)
    const mockFatherUrl = `https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/uploads/test-user/${sessionId}/original/v1-father.jpg`;
    const mockMotherUrl = `https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/uploads/test-user/${sessionId}/original/v1-mother.jpg`;

    console.log('✅ Simulated upload successful!');
    console.log(`📁 Father image URL: ${mockFatherUrl}`);
    console.log(`📁 Mother image URL: ${mockMotherUrl}`);

    console.log('📤 Creating generation job...');

    const createResponse = await fetch(`${API_BASE_URL}/api/baby-generator/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fatherImageUrl: mockFatherUrl,
        motherImageUrl: mockMotherUrl,
        gender: 'babyBoy',
        sessionId: sessionId,
        fatherUploadKey: `uploads/test-user/${sessionId}/original/v1-father.jpg`,
        motherUploadKey: `uploads/test-user/${sessionId}/original/v1-mother.jpg`
      }),
    });

    const createData = await createResponse.json();

    if (createData.code !== 0) {
      throw new Error(`Create job failed: ${createData.message}`);
    }

    console.log('✅ Job created successfully!');
    console.log(`📋 Job ID: ${createData.data.jobId}`);
    console.log(`🔗 Session ID: ${createData.data.sessionId}`);
    console.log(`👤 User UUID: ${createData.data.userUuid}\n`);

    // Test status checking
    await testJobStatus(createData.data.jobId);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

async function testJobStatus(jobId) {
  console.log('📊 Testing job status...');
  
  try {
    const statusResponse = await fetch(`${API_BASE_URL}/api/baby-generator/status/${jobId}`);
    const statusData = await statusResponse.json();
    
    if (statusData.code !== 0) {
      throw new Error(`Status check failed: ${statusData.message}`);
    }

    console.log('✅ Status check successful!');
    console.log(`📈 Status: ${statusData.data.status}`);
    
    if (statusData.data.result && statusData.data.result.length > 0) {
      console.log(`🖼️  Generated images: ${statusData.data.result.length}`);
      statusData.data.result.forEach((url, index) => {
        console.log(`   ${index + 1}. ${url}`);
      });
    }

  } catch (error) {
    console.error('❌ Status test failed:', error.message);
  }
}

async function testDirectAPI() {
  console.log('\n🔗 Testing direct MaxStudio API...');
  
  try {
    const response = await fetch('https://api.maxstudio.ai/baby-generator', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
      },
      body: JSON.stringify({
        fatherImage: 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-father.jpg',
        motherImage: 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-mother.png',
        gender: 'babyBoy'
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Direct API test successful!');
    console.log(`📋 Job ID: ${data.jobId}`);

  } catch (error) {
    console.error('❌ Direct API test failed:', error.message);
    console.log('ℹ️  This might be expected if the API key is not valid or the service is not available.');
  }
}

async function main() {
  console.log('🍼 AI Baby Generator API Test Suite\n');
  console.log(`🌐 API Base URL: ${API_BASE_URL}`);
  console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...\n`);

  // Test our API endpoints with R2 upload flow
  await testUploadAndCreate();

  // Test direct API (optional)
  await testDirectAPI();

  console.log('\n✨ Test suite completed!');
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testUploadAndCreate,
  testJobStatus,
  testDirectAPI
};
