#!/usr/bin/env node

/**
 * Final test to verify the double prefix fix
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';

async function testFinalFix() {
  console.log('🔧 Testing Final Double Prefix Fix\n');

  try {
    const sessionId = `final_test_${Date.now()}`;
    const mockUserUuid = 'test-user-uuid';
    
    // Expected paths after removing duplicate ai-baby-generator prefix
    const expectedKey = `uploads/${mockUserUuid}/${sessionId}/original/v1-father.jpg`;
    const expectedUrl = `https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/${expectedKey}`;
    
    // What the actual Cloudflare R2 URL should be (with single ai-baby-generator from bucket config)
    const actualR2Url = `https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/${expectedKey}`;

    console.log('✅ Expected results after fix:');
    console.log(`📁 Generated Key: ${expectedKey}`);
    console.log(`🔗 API Response URL: ${expectedUrl}`);
    console.log(`🌐 Actual R2 URL: ${actualR2Url}`);

    // Verify path structure
    const analysis = {
      keyStartsWithUploads: expectedKey.startsWith('uploads/'),
      keyHasNoDoublePrefix: !expectedKey.includes('ai-baby-generator'),
      urlHasSinglePrefix: actualR2Url.split('ai-baby-generator').length === 2, // Should appear only once
      urlStructureCorrect: actualR2Url.includes('/ai-baby-generator/uploads/'),
    };

    console.log('\n🔍 Path Structure Analysis:');
    console.log(`✅ Key starts with uploads/: ${analysis.keyStartsWithUploads}`);
    console.log(`✅ Key has no ai-baby-generator prefix: ${analysis.keyHasNoDoublePrefix}`);
    console.log(`✅ URL has single ai-baby-generator prefix: ${analysis.urlHasSinglePrefix}`);
    console.log(`✅ URL structure is correct: ${analysis.urlStructureCorrect}`);

    const allValid = Object.values(analysis).every(Boolean);
    console.log(`\n🎯 Overall fix status: ${allValid ? '✅ CORRECT' : '❌ NEEDS WORK'}`);

    // Compare with the problematic double prefix
    console.log('\n📋 Before vs After:');
    console.log(`❌ Before (double prefix): https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/ai-baby-generator/uploads/...`);
    console.log(`✅ After (single prefix):  https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/...`);

    // Test endpoint availability
    console.log('\n🧪 Testing upload endpoint...');
    try {
      const response = await fetch(`${API_BASE_URL}/api/baby-generator/upload`, {
        method: 'POST',
      });

      console.log(`📡 Upload endpoint status: ${response.status}`);
      
      if (response.status === 400) {
        console.log('✅ Upload endpoint is working (400 for missing params is expected)');
      }

    } catch (error) {
      console.log('ℹ️  Server not running. Start with: pnpm dev');
    }

    console.log('\n📝 Summary of Changes:');
    console.log('- Removed ai-baby-generator/ prefix from code-generated keys');
    console.log('- Storage bucket config already provides the ai-baby-generator prefix');
    console.log('- This prevents double prefix in final URLs');
    console.log('- Generated keys: uploads/... (no prefix)');
    console.log('- Final URLs: .../ai-baby-generator/uploads/... (single prefix from bucket)');

    return {
      expectedKey,
      expectedUrl,
      actualR2Url,
      analysis,
      allValid,
    };

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return null;
  }
}

async function main() {
  console.log('🍼 Final Double Prefix Fix Verification\n');
  console.log(`🌐 API Base URL: ${API_BASE_URL}\n`);

  const result = await testFinalFix();

  if (result && result.allValid) {
    console.log('\n🎉 All validations passed!');
    console.log('✅ The double prefix issue has been resolved.');
    console.log('\n🚀 Next steps:');
    console.log('1. Start server: pnpm dev');
    console.log('2. Test upload: http://localhost:3000/test-upload-debug');
    console.log('3. Verify the returned URL matches the actual R2 location');
  } else {
    console.log('\n❌ Some validations failed.');
    console.log('Please review the implementation.');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testFinalFix
};
