#!/usr/bin/env node

/**
 * Test script to verify the status API fix
 * This script tests the corrected API response handling
 */

const API_KEY = '2af893ab-6a1f-4557-8ec0-081b275371a1';
const API_BASE_URL = 'https://api.maxstudio.ai';
const LOCAL_API_BASE = 'http://localhost:3001';

// Use the same URLs from previous successful test
const FATHER_IMAGE_URL = 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-father.jpg';
const MOTHER_IMAGE_URL = 'https://pub-8161ccab57914028b88611bdf39ec874.r2.dev/ai-baby-generator/uploads/anonymous/session_1754367445634_0zzy5y13r/original/v1-mother.png';

async function testDirectAPI() {
  console.log('🧪 Testing Direct MaxStudio AI API...\n');

  try {
    // Step 1: Create generation job
    console.log('📤 Creating generation job...');
    
    const createResponse = await fetch(`${API_BASE_URL}/baby-generator`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
      },
      body: JSON.stringify({
        fatherImage: FATHER_IMAGE_URL,
        motherImage: MOTHER_IMAGE_URL,
        gender: 'babyBoy'
      }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`Create request failed: ${createResponse.status} - ${errorText}`);
    }

    const createData = await createResponse.json();
    console.log('✅ Job created successfully!');
    console.log('📋 Create response:', JSON.stringify(createData, null, 2));

    const jobId = createData.jobId;
    console.log(`🆔 Job ID: ${jobId}\n`);

    // Step 2: Test status check
    console.log('📊 Testing status check...');
    
    const statusResponse = await fetch(`${API_BASE_URL}/baby-generator/${jobId}`, {
      method: 'GET',
      headers: {
        'x-api-key': API_KEY,
      },
    });

    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      throw new Error(`Status request failed: ${statusResponse.status} - ${errorText}`);
    }

    const statusData = await statusResponse.json();
    console.log('📥 Status response:', JSON.stringify(statusData, null, 2));
    
    // Show the structure we need to handle
    console.log('\n🔍 Response structure analysis:');
    console.log(`- Has 'code' field: ${statusData.hasOwnProperty('code')}`);
    console.log(`- Has 'data' field: ${statusData.hasOwnProperty('data')}`);
    console.log(`- Status location: ${statusData.data ? 'data.status' : 'direct.status'}`);
    console.log(`- Status value: ${statusData.data?.status || statusData.status}`);
    console.log(`- Result location: ${statusData.data ? 'data.result' : 'direct.result'}`);
    console.log(`- Result value: ${JSON.stringify(statusData.data?.result || statusData.result)}`);

    return { jobId, statusData };

  } catch (error) {
    console.error('❌ Direct API test failed:', error.message);
    throw error;
  }
}

async function testOurAPI(jobId) {
  console.log('\n🔧 Testing Our Fixed API...\n');

  try {
    // Test our status endpoint
    console.log('📊 Testing our status endpoint...');
    
    const statusResponse = await fetch(`${LOCAL_API_BASE}/api/baby-generator/status/${jobId}`);
    
    if (!statusResponse.ok) {
      const errorText = await statusResponse.text();
      throw new Error(`Our status API failed: ${statusResponse.status} - ${errorText}`);
    }
    
    const statusData = await statusResponse.json();
    console.log('📥 Our API response:', JSON.stringify(statusData, null, 2));
    
    if (statusData.code !== 0) {
      throw new Error(`Our status API failed: ${statusData.message}`);
    }

    console.log('✅ Our status API works!');
    console.log(`📈 Status: ${statusData.data.status}`);
    console.log(`🖼️  Results: ${statusData.data.result?.length || 0} images`);

    return statusData;

  } catch (error) {
    console.error('❌ Our API test failed:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🔧 Status API Fix Test\n');
  console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);
  console.log(`🌐 MaxStudio API: ${API_BASE_URL}`);
  console.log(`🏠 Local API: ${LOCAL_API_BASE}\n`);

  try {
    // Test the direct MaxStudio API first to understand the response format
    const { jobId, statusData } = await testDirectAPI();
    
    // Test our fixed API
    await testOurAPI(jobId);

    console.log('\n✨ All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Job ID: ${jobId}`);
    console.log(`- Status: ${statusData.data?.status || statusData.status}`);
    console.log(`- Results: ${(statusData.data?.result || statusData.result)?.length || 0} images`);

  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testDirectAPI,
  testOurAPI
};
